{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Product Interface</title>
    <style>
        .container {
            max-width: 90%;
            margin-top: 15px;
            background: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        #product-description {
            height: 120px;
            /* 初始高度 */
            min-height: 120px;
            /* 最小高度 */
            max-height: 600px;
            /* 设置最大高度 */
            overflow-y: auto;
            /* 内容超出最大高度时出现滚动条 */
            resize: vertical;
            /* 允许用户垂直调整大小 */
            padding: 10px;
            width: 100%;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 10px;
        }

        #product-description.dragover {
            background-color: #00E676;
        }

        .input-container {
            position: relative;
            display: inline-block;
            width: calc(100% - 20px);
        }

        #loading-spinner {
            position: absolute;
            top: calc((100% - 20px) / 2);
            left: calc((100% - 20px) / 2);
            transform: translate(-50%, -50%);
            z-index: 10;
            /* 确保加载符号位于输入框之上 */
            animation: spin 1s linear infinite;
        }

        #select-mode {
            position: absolute;
            top: calc((100% - 20px) / 2);
            left: calc((100% - 20px) / 2);
            transform: translate(-50%, -50%);
            z-index: 10;
            /* 确保加载符号位于输入框之上 */
            font-size: 0.75rem;
        }

        #content-description {
            width: calc(100% - 20px);
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        select {
            padding: 10px;
            margin-top: 10px;
            width: 200px;
            border: 1px solid #ddd;
            border-radius: 5px;
            outline: none;
        }

        #response {
            margin-top: 10px;
            padding: 10px;
            background: #e7e7e7;
            border-radius: 5px;
        }

        #outline {
            margin-top: 10px;
            width: calc(100% - 20px);
            padding: 10px;
            background: #e7e7e7;
            border-radius: 5px;
            border: 1px solid #ddd;
            overflow-y: auto;
            /* 启用垂直滚动条 */
            resize: vertical;
            /* 禁用手动调整大小，防止用户超出设置的最大高度 */
            max-height: 500px;
            /* 设置最高高度为300px */
            height: auto;
            /* 初始高度 */
            min-height: 100px;
            /* 设置最大高度 */

        }

        .copy-button {
            cursor: pointer;
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            margin-top: 5px;
            margin-left: auto;
            display: block;
        }

        /* 定义加载指示器的样式 */
        .loader-container {
            /* New container for the loader */
            display: flex;
            justify-content: flex-start;
            align-items: center;
            /* This centers the loading indicator vertically */
        }

        .loadingIndicator {
            border: 4px solid #f3f3f3;
            /* Light grey */
            border-top: 4px solid #3498db;
            /* Blue */
            border-radius: 50%;
            width: 15px;
            height: 15px;
            animation: spin 1s linear infinite;
            margin-left: 20px;
        }

        .btn-xs {
            padding: 0.1rem 0.4rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: 0.2rem;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .font-label {
            font-size: 17px;
            /* 设置字体大小 */
            color: #0e0d0d;
            /* 字体颜色 */

        }

        .font-label:hover {
            color: #0e0d0d;
            /* 鼠标悬浮时保持颜色不变 */
            text-decoration: none;
            /* 取消任何文本装饰 */
        }

        .example-row {
            display: flex;
            align-items: center;
            /* 垂直居中对齐 */
            margin-bottom: 10px;
            /* 行间距 */
        }

        .example-label {
            margin-right: 5px;
            /* 标签与输入框的距离 */
            font-size: 1em;
        }

        .example-textarea {
            height: 2em;
            /* 调整高度 */
            min-height: 2em;
            padding-left: 10px;
            /* 增加左侧内边距 */
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 80%;
            color: #000;
            /* 设置初始文本颜色为浅灰色 */
            font-size: 1em;
            /* 调整字体大小 */
        }

        .example-textarea::placeholder {
            color: #aaa;
            /* 设置 placeholder 文本颜色为浅灰色 */
            opacity: 1;
            /* 确保颜色应用在所有浏览器中 */
        }

        .example-textarea:focus {
            outline: none;
            /* 去掉默认的聚焦外边框 */
            color: #000;
            /* 聚焦时文本颜色变为黑色 */
        }

        .custom-checkbox {
            width: 18px;
            height: 18px;
            vertical-align: bottom;
            /* 将复选框底部对齐 */
            margin-right: 0px;
            /* 适当的右边距 */
            position: relative;
            top: -12px;
            /* 根据需要调整 */
        }

        /* 图片上传相关样式 */
        .image-upload-container {
            margin-top: 10px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background-color: #f9f9f9;
            transition: all 0.3s ease;
        }

        .image-upload-container.dragover {
            border-color: #00E676;
            background-color: #e8f5e8;
        }

        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .image-preview {
            position: relative;
            display: inline-block;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .image-preview img {
            max-width: 150px;
            max-height: 150px;
            object-fit: cover;
            display: block;
        }

        .image-info {
            padding: 5px 8px;
            font-size: 12px;
            color: #666;
            background: #f5f5f5;
            border-top: 1px solid #ddd;
        }

        .image-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-remove:hover {
            background: rgba(255, 0, 0, 1);
        }

        .upload-hint {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .file-input-button {
            background: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .file-input-button:hover {
            background: #45a049;
        }
    </style>
</head>

<body>
    <div class="container">
        <div style="padding-bottom: 10px">
            <a class="font-label">模型选择：</a>
            <select id="modelSelect" style="height: 40px;">
                {% for model in models %}
                <option value="{{ model.name }}">{{ model.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div style="padding-bottom: 10px">
            <a class="font-label">品牌选择：</a>
            <select id="brand_select" style="height: 40px;">
                {% for brand in brands %}
                <option value="{{ brand.name }}">{{ brand.name }}</option>
                {% endfor %}
            </select>
            <a id="plata" style="margin-left: 20px;" class="font-label">发布平台：</a>
            <select id="platform_select" style="height: 40px;">
                {% for platform in platforms %}
                <option value="{{ platform.name }}">{{ platform.name }}</option>
                {% endfor %}
            </select>
        </div>
        <!-- 新增的产品信息输入部分 -->
        <div class="new-input-container" style="padding-bottom: 10px">
            <a class="font-label">产品名称：</a>
            <input id="product_name" placeholder="请输入产品名称" type="text"
                style="border: 1px solid #ddd; border-radius: 5px; height: 40px;">
            <a class="font-label" style="margin-left: 20px">产品受众：</a>
            <input id="product_audience" placeholder="请输入产品受众" type="text"
                style="border: 1px solid #ddd; border-radius: 5px; height: 40px;">
            <a class="font-label" style="margin-left: 20px">产品卖点：</a>
            <input id="product_selling_points" placeholder="请输入产品卖点"
                style="border: 1px solid #ddd; border-radius: 5px; height: 40px;">
        </div>

        <!-- 结束新增部分 -->
        <div id="copytheme2" style="padding-bottom: 10px">
            <a class="font-label">文案主题：</a>
            <input id="copytheme2_input" placeholder="如沙漠、海洋等" type="text"
                style="border: 1px solid #ddd; border-radius: 5px; height: 40px;">
        </div>

        <div id="copyinfo" style="padding-bottom:15px; display: none;">
            <a class="font-label">文案类型：</a>
            <select id="copytype">
                <option value="New launch">New launch</option>
                <option value="Festival">Festival</option>
                <option value="Life style">Life style</option>
                <option value="Campaign">Campaign</option>
            </select>
            <a class="font-label" style="margin-left: 20px">文案主题：</a>
            <input id="copytheme" type="text" style="border: 1px solid #ddd; border-radius: 5px;height: 40px;">
        </div>
        <div id="productInfoBox" style="padding-bottom: 5px">
            <label for="ifuseproductinfo" class="font-label">产品资料等相关信息</label>
        </div>

        <!-- 移除复选框并保持产品信息输入框始终可见 -->
        <div id="productInfo">
            <div class="input-container" style="position: relative; display: inline-block;">
                <textarea type="text" id="product-description" rows="5"
                    placeholder="可以在此处填写产品资料等相关信息及特点,&#10;也可以直接拖动相关的介绍文件至此窗口进行识别文档内部信息&#10;&#10;您也可以直接粘贴图片(Ctrl+V)或点击下方上传按钮添加图片"></textarea>
                <div id="loading-spinner" style="display: none;">
                    <img src="{% static 'pic/xuanzhuan.png' %}" alt="Loading..." style="width: 20px; height: 20px;">
                </div>
                <div id="select-mode" style="display: none;">
                    <div>
                        <input type="radio" value="0" id="box1" name="singleOption" checked>
                        <label for="box1" style="font-size: 20px;">文字模式</label>
                    </div>
                    <div>
                        <input type="radio" value="1" id="box2" name="singleOption">
                        <label for="box2" style="font-size: 20px;">兼容模式</label>
                    </div>
                    <div>
                        <input type="radio" value="2" id="box3" name="singleOption">
                        <label for="box3" style="font-size: 20px;">图片模式</label>
                    </div>
                    <button class="btn btn-success btn-sm btn-xs" onclick="draginfo()" style="font-size: 20px;">确定
                    </button>
                    <img src="{% static 'pic/prompt.png' %}"
                        style="cursor:pointer; width: 20px; height: 20px; margin-left: 5px"
                        title="文字模式适合PPT中图片中文字较少的情况,图片模式适合PPT中图片较多的情况">
                </div>
            </div>

            <!-- 图片上传区域 -->
            <div class="image-upload-container" id="image-upload-area">
                <div class="upload-hint">
                    <p>📷 图片上传区域</p>
                    <p>支持拖拽图片到此处，或点击按钮选择文件，也可以直接粘贴图片 (Ctrl+V)</p>
                    <p>支持格式：JPG, PNG, GIF, WEBP</p>
                </div>
                <input type="file" id="file-input" accept="image/*" multiple style="display: none;">
                <button class="file-input-button" onclick="document.getElementById('file-input').click()">
                    选择图片文件
                </button>
                <button class="file-input-button" onclick="testImageUpload()" style="margin-left: 10px; background: #2196F3;">
                    测试图片数据
                </button>
            </div>

            <!-- 图片预览区域 -->
            <div class="image-preview-container" id="image-preview-container">
                <!-- 上传的图片预览将在这里显示 -->
            </div>
        </div>
        <!-- 复选框部分 -->

        <div id="content-description-box" style="padding-bottom: 5px;padding-top: 5px">
            <input type="checkbox" id="ifusecontentdescription" value="1" class="custom-checkbox">
            <label for="ifusecontentdescription" class="font-label">文案平台撰写要求</label>
        </div>

        <!-- 隐藏的文本输入框 -->
        <div id="content-description-container" style="display: none; padding-bottom: 10px;">
            <textarea type="text" id="content-description" rows="4"
                placeholder="可以在此处填入对平台文案的要求：字数限制、风格要求等&#10;例如：&#10;抖音快手平台要求：&#10;产品文案：简单、有趣味性，贴合产品特点，长度上控制在20字内&#10;制定评论要求：以好懂的语言描述科普介绍产品细节，最后引导前往官网探索，整体控制在三句话之内"
                style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;height: 145px;min-height: 145px"></textarea>
        </div>

        <div id="examplebox" style="padding-bottom:5px ">
            <input type="checkbox" id="ifuseexample" value="1" class="custom-checkbox">
            <label for="ifuseexample" class="font-label">自定义学习示例文案</label>
        </div>

        <div id="examples" style="display:none;">

            <div class="example-row">
                <label>示例文案：</label>
                <label for="example1" class="example-label" style="margin-left: 3px">1.</label>
                <textarea class="example-textarea" rows="1" id="example1" placeholder="可以输入你想要进行学习的风格示例文案"></textarea>
            </div>
            <div class="example-row">
                <label for="example2" class="example-label" style="margin-left: 84px">2.</label>
                <textarea class="example-textarea" rows="1" id="example2" placeholder="可以输入你想要进行学习的风格示例文案"></textarea>
            </div>
            <div class="example-row">
                <label for="example3" class="example-label" style="margin-left: 84px">3.</label>
                <textarea class="example-textarea" rows="1" id="example3" placeholder="可以输入你想要进行学习的风格示例文案"></textarea>
            </div>
        </div>


        <div id="get_outline" style="display: none;">
            <div class="loader-container">
                <button class="btn btn-primary" id="generate2" onclick="submitAIRequest2()">生成大纲</button>
                <div id="loadingIndicator2" class="loadingIndicator" style="display: none"></div>
            </div>
            <div>
                <textarea id="outline" rows="5">文案大纲将在此处生成...</textarea>
            </div>
        </div>

        <div class="loader-container">
            <button class="btn btn-primary" id="generate" onclick="submitAIRequest()">生成文案</button>
            <div id="loadingIndicator" class="loadingIndicator" style="display: none"></div>
        </div>

        <div id="response" style="width: calc(100% - 20px)">
            <p id="ai-response">文案将在此处生成...</p>
            <div style="display: flex;justify-content: flex-end;gap: 10px">
                <button class="btn btn-success btn-sm" onclick="copyToClipboard()">拷贝</button>
                {# <button class="btn btn-success btn-sm" onclick="save()">保存云端</button>#}
            </div>
        </div>

    </div>
    <script src="{% static 'js/drag.js' %}"></script>
    <script>
        // 图片上传相关变量
        let uploadedImages = [];

        // 图片上传功能初始化
        document.addEventListener('DOMContentLoaded', function() {
            initImageUpload();
        });

        function initImageUpload() {
            const uploadArea = document.getElementById('image-upload-area');
            const fileInput = document.getElementById('file-input');
            const previewContainer = document.getElementById('image-preview-container');
            const productDescription = document.getElementById('product-description');

            // 文件选择处理
            fileInput.addEventListener('change', function(e) {
                handleFiles(e.target.files);
            });

            // 拖拽上传
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });

            // 粘贴图片功能
            document.addEventListener('paste', function(e) {
                const items = e.clipboardData.items;
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        e.preventDefault();
                        const file = items[i].getAsFile();
                        handleFiles([file]);
                        break;
                    }
                }
            });

            // 为textarea添加粘贴事件
            productDescription.addEventListener('paste', function(e) {
                const items = e.clipboardData.items;
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        e.preventDefault();
                        const file = items[i].getAsFile();
                        handleFiles([file]);
                        break;
                    }
                }
            });
        }

        function handleFiles(files) {
            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        addImagePreview(file, e.target.result);
                    };
                    reader.readAsDataURL(file);
                }
            }
        }

        function addImagePreview(file, dataUrl) {
            console.log('添加图片预览:', file.name, '大小:', file.size);
            const previewContainer = document.getElementById('image-preview-container');
            const imageId = 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // 添加到上传图片数组
            uploadedImages.push({
                id: imageId,
                file: file,
                dataUrl: dataUrl,
                name: file.name,
                size: file.size
            });

            console.log('当前上传图片数量:', uploadedImages.length);

            // 创建预览元素
            const previewDiv = document.createElement('div');
            previewDiv.className = 'image-preview';
            previewDiv.id = imageId;

            const img = document.createElement('img');
            img.src = dataUrl;
            img.alt = file.name;

            const infoDiv = document.createElement('div');
            infoDiv.className = 'image-info';
            infoDiv.innerHTML = `${file.name}<br>${formatFileSize(file.size)}`;

            const removeBtn = document.createElement('button');
            removeBtn.className = 'image-remove';
            removeBtn.innerHTML = '×';
            removeBtn.onclick = function() {
                removeImage(imageId);
            };

            previewDiv.appendChild(img);
            previewDiv.appendChild(infoDiv);
            previewDiv.appendChild(removeBtn);
            previewContainer.appendChild(previewDiv);
        }

        function removeImage(imageId) {
            // 从数组中移除
            uploadedImages = uploadedImages.filter(img => img.id !== imageId);

            // 从DOM中移除
            const element = document.getElementById(imageId);
            if (element) {
                element.remove();
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取上传的图片数据（用于发送到后端）
        function getUploadedImagesData() {
            console.log('获取图片数据，当前上传的图片数量:', uploadedImages.length);
            console.log('上传的图片详情:', uploadedImages);
            return uploadedImages.map(img => ({
                id: img.id,
                name: img.name,
                size: img.size,
                dataUrl: img.dataUrl
            }));
        }

        // 测试图片上传功能
        function testImageUpload() {
            const imageData = getUploadedImagesData();
            alert(`当前上传了 ${imageData.length} 张图片\n\n详情请查看浏览器控制台`);
            console.log('测试图片数据:', imageData);
        }

        //生成文案
        function submitAIRequest() {
            const generateButtonEl = document.getElementById("generate");
            const productDescriptionEl = document.getElementById('product-description');
            const productoutline = document.getElementById('outline');
            const theme = document.getElementById('copytheme');
            const type = document.getElementById('copytype');
            const copytheme2_input = document.getElementById('copytheme2_input')
            // 从缓存的DOM元素获取值
            let productIntroduction = productDescriptionEl.value;
            var model = document.getElementById("modelSelect").value;
            var source = 1;
            var outline = productoutline.value;
            var brand = document.getElementById('brand_select').value;
            var platform = document.getElementById('platform_select').value;
            var copytheme = theme.value;
            var copytheme2 = copytheme2_input.value;
            var copytype = type.value;

            //产品
            const productName = document.getElementById('product_name').value;
            const productAudience = document.getElementById('product_audience').value;
            const productSellingPoints = document.getElementById('product_selling_points').value;

            // 文案风格要求
            var useContentDescription = document.getElementById("ifusecontentdescription").checked;
            var contentDescriptionText = useContentDescription ? document.getElementById("content-description").value : "";
            var ifuseexamples = ifuseexample.checked ? 1 : 0;
            var example1 = document.getElementById('example1').value;
            var example2 = document.getElementById('example2').value;
            var example3 = document.getElementById('example3').value;

            // 获取上传的图片数据
            var uploadedImagesData = getUploadedImagesData();

            generateButtonEl.disabled = true;
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    productIntroduction,
                    model,
                    source,
                    outline,
                    brand,
                    platform,
                    copytheme,
                    copytype,
                    ifuseexamples,
                    example1,
                    example2,
                    example3,
                    copytheme2,
                    useContentDescription,
                    contentDescriptionText,
                    productName,
                    productAudience,
                    productSellingPoints,
                    uploadedImages: uploadedImagesData

                }));
                $('#loadingIndicator').css("display", "inline-block")
            } else {
                // 可以在这里通知用户WebSocket未连接
                $('#loginModal').modal('show')
                generateButtonEl.disabled = false;
                console.error("连接失败，请刷新页面重试");

                // 可以在这里禁用生成按钮，或者显示一条消息
            }
            messageBuffer = '';
        }

        //生成文案大纲
        function submitAIRequest2() {
            const generateButtonEl = document.getElementById("generate2");
            const productDescriptionEl = document.getElementById('product-description');
            // 从缓存的DOM元素获取值
            let product = productDescriptionEl.value;
            var model = document.getElementById("modelSelect").value;
            var source = 2;
            generateButtonEl.disabled = true;
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({ product, model, source }));
                $('#loadingIndicator2').css("display", "inline-block")
            } else {
                // 可以在这里通知用户WebSocket未连接
                $('#loginModal').modal('show')
                generateButtonEl.disabled = false;
                console.error("连接失败，请刷新页面重试");

                // 可以在这里禁用生成按钮，或者显示一条消息
            }
            messageBuffer = '';
        }

        socket.onmessage = function (event) {
            const data = JSON.parse(event.data);
            // console.log("WebSocket返回的数据：", data);  // 打印返回的数据到控制台
            if (data["target"] === 1) {
                const aiResponseEl = document.getElementById('ai-response');
                const generateButtonEl = document.getElementById("generate");
                if ("message" in data) {
                    messageBuffer += data.message;
                    if (aiResponseEl) {
                        aiResponseEl.innerHTML = messageBuffer;
                    }
                    // { #console.log('Message from server ', data.message);# }
                }
                if ("status" in data) {
                    if (data.status === 1) {
                        console.log("完毕")
                    } else if (data.status === 0) {
                        console.log("服务器网络故障")
                    }
                    $('#loadingIndicator').css("display", "none")
                    generateButtonEl.disabled = false;
                }

            } else if (data["target"] === 2) {
                const aiResponseEl = document.getElementById('outline');
                const generateButtonEl = document.getElementById("generate2");
                if ("message" in data) {
                    messageBuffer += data.message;
                    messageBuffer = messageBuffer.replace(/<br>/g, '\n');
                    if (aiResponseEl) {
                        aiResponseEl.value = messageBuffer;
                    }
                    // { #console.log('Message from server ', data.message);# }
                }
                if ("status" in data) {
                    if (data.status === 1) {
                        console.log("完毕")
                    } else if (data.status === 0) {
                        console.log("服务器网络故障")
                    }
                    $('#loadingIndicator2').css("display", "none")
                    generateButtonEl.disabled = false;
                }

            }
        };


        function copyToClipboard() {
            // 优化错误处理
            const aiResponseEl = document.getElementById('ai-response');
            navigator.clipboard.writeText(aiResponseEl.textContent).then(function () {
                alert('已复制到剪贴板');
            }).catch(function (error) {
                console.error('Error in copying text: ', error);
                alert('Error in copying text: ' + error);
            });
        }

        // 选择某个选项，则隐藏部分功能
        function toggleBrandSelect() {
            var brandSelect = document.getElementById("brand_select");
            var platform = document.getElementById("platform_select");
            var outline = document.getElementById("get_outline")
            var platinfo = document.getElementById("plata");
            var copyinfo = document.getElementById("copyinfo")
            var copytheme2 = document.getElementById("copytheme2")
            if (brandSelect.value === "DLOCCOLD") {
                // Hide platform_select
                platform.style.display = "none";
                platinfo.style.display = "none";
                outline.style.display = "none";
                copyinfo.style.display = "block";
                copytheme2.style.display = "none";
                //examplebox.style.display = "inline";
            } else {
                // Show platform_select
                platform.style.display = "inline";
                platinfo.style.display = "inline";
                if (platform.value === "公众号") {
                    outline.style.display = "inline";
                }
                copyinfo.style.display = "none";
                copytheme2.style.display = "block";
                //examplebox.style.display = "none";
            }
        }

        //选择某个选框后显示或隐藏某个选项
        function exampleHideShow() {
            var examples = document.getElementById("examples")
            var ifuseexample = document.getElementById("ifuseexample")
            if (ifuseexample.checked) {
                examples.style.display = "block";
            } else {
                examples.style.display = "none";
            }

        }

        function togglePlatformSelect() {
            var platform = document.getElementById("platform_select");
            var outline = document.getElementById("get_outline")
            if (platform.value === "公众号") {
                outline.style.display = "block";
            } else {
                outline.style.display = "none";
            }
        }

        // 1. 处理品牌选择变化
        var brandSelect = document.getElementById("brand_select");
        brandSelect.addEventListener("change", toggleBrandSelect);
        // 2. 处理使用示例文案的复选框
        var ifuseexample = document.getElementById("ifuseexample")
        ifuseexample.addEventListener("change", exampleHideShow);
        // 3. 处理平台选择变化
        var platformSelect = document.getElementById("platform_select");
        platformSelect.addEventListener("change", togglePlatformSelect);

        // 4. 处理内容描述复选框
        var ifusecontentdescription = document.getElementById("ifusecontentdescription");
        ifusecontentdescription.addEventListener("change", contentDescriptionHideShow);

        // 函数：显示或隐藏产品文案要求输入框
        function contentDescriptionHideShow() {
            var container = document.getElementById("content-description-container");
            if (ifusecontentdescription.checked) {
                container.style.display = "block";
            } else {
                container.style.display = "none";
            }
        }

        document.getElementById('product-description').addEventListener('input', function () {
            this.style.height = 'auto'; // 重置高度
            this.style.height = Math.min(this.scrollHeight, 500) + 'px'; // 设置新高度，不超过最大高度
        });
        document.getElementById('outline').addEventListener('input', function () {
            this.style.height = 'auto'; // 重置高度
            this.style.height = Math.min(this.scrollHeight, 600) + 'px'; // 自动调整高度，但不超过最大高度
        });


    </script>
</body>

</html>