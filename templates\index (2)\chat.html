{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Interface</title>
    <!-- 顺序不变，否则会出现主题改变 -->
    <!-- 引入 CSS highlight主题样式和bootstrap样式-->
    <link rel="stylesheet" href="{% static 'css/atom-one-dark.min.css' %}">

    <!-- 引入Js highlight库，marked库和富文本编辑器-->
    <script src="{% static 'js/http_cdnjs.cloudflare.com_ajax_libs_highlight.js_11.5.1_highlight.min.js' %}"></script>
    <script src="{% static 'js/http_cdn.jsdelivr.net_npm_marked_marked.min.js' %}"></script>
    <script src="{% static 'js/http_unpkg.com_@wangeditor_editor@latest_dist_index.js' %}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 85%;
            /* max-width: 800px; */
            margin: 20px auto;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #fff;
        }

        .chat-container {
            height: calc(100vh - 250px);
            overflow-y: auto;
            padding: 20px;
            border-radius: 10px;
            background-color: #f5f5f5;
            margin-bottom: 20px;
        }

        .message {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 10px;
            background-color: #ffffff;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .message p {
            margin: 0;
        }

        .message-content {
            margin-top: 5px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .form-control {
            border-radius: 20px;
        }

        .btn-send {
            border-radius: 20px;
            transition: background-color 0.3s, border-color 0.3s;
        }

        .btn-send:focus {
            outline: none;
            box-shadow: none;
        }

        .btn-send.blur {
            background-color: #cecece;
            border-color: #cecece;
        }

        .btn-send.success {
            background-color: #4CAF50;
            border-color: #4CAF50;
        }

        .hljs {
            background-color: #2b2b2b;
            white-space: pre;
            overflow-x: auto;
            width: 43rem;
            border-radius: 5px;
        }

        #user-question {
            max-height: 100px;
            /* Set the maximum height */
            overflow-y: auto;
            /* Enable vertical scrolling */
            height: 40px;

        }
    </style>
</head>

<body>
    <div class="container">
        <div class="chat-container" id="chat-box"></div>
        <div class="input-group">
            <label for="user-question"></label>
            <textarea type="text" id="user-question" onfocus="textareaFocus()" onblur="textareaBlur()"
                class="form-control" placeholder="Ask a question..." onKeyDown="handleKeyDown(event)"></textarea>

            <div class="input-group-append">
                <button class="btn btn-send blur" id="submitBtn" onclick="submitQuestion()">Send</button>
            </div>
        </div>
    </div>
    <script>
        function textareaFocus() {
            document.getElementById('submitBtn').classList.remove('blur');
            document.getElementById('submitBtn').classList.add('success');
        }

        function textareaBlur() {
            document.getElementById('submitBtn').classList.remove('success');
            document.getElementById('submitBtn').classList.add('blur');
        }

        function detectAndHighlightCode(input) {
            // 简化后的正则表达式，用于匹配 Markdown 格式的代码块
            const codeBlockPattern = /```[\s\S]+?```/g;
            let match;
            let lastIndex = 0;
            let formattedText = '';

            // 使用 while 循环查找所有代码块
            while ((match = codeBlockPattern.exec(input)) !== null) {
                // 添加代码块之前的普通文本部分
                formattedText += marked.parse(input.substring(lastIndex, match.index));

                // 去掉代码块的起始和结束标志，获取代码内容
                const code = match[0].slice(3, -3).trim();
                // 使用 highlight.js 自动检测语言并进行高亮处理
                const highlightedCode = hljs.highlightAuto(code);
                // 将高亮处理后的代码块添加到 formattedText 中
                formattedText += `<pre><code class="hljs ${highlightedCode.language}">${highlightedCode.value}</code></pre>`;
                // 更新 lastIndex 为当前匹配结束的位置，以便处理下一个代码块
                lastIndex = codeBlockPattern.lastIndex;
            }
            // console.log(formattedText)
            // 添加最后一个代码块后面的剩余文本
            formattedText += marked.parse(input.substring(lastIndex));
            return formattedText; // 返回处理后的 HTML 字符串
        }

        var Temp = document.createElement("div");
        function submitQuestion() {
            const question = document.getElementById("user-question").value;
            messageBuffer = ""
            if (question.trim() !== "") {
                // 显示用户的问题
                const userMessage = document.createElement("div");
                userMessage.classList.add("message");
                userMessage.innerHTML = "<p>You:</p> <div class='message-content'>" + marked.parse(question) + "</div>";
                document.getElementById("chat-box").appendChild(userMessage);

                // 响应测试例子
                var source = 4
                var model = ""
                messages = document.getElementById("chat-box").getElementsByClassName('message')
                let allMessages = [];
                for (let message of messages) {
                    allMessages.push(message.textContent);
                }
                var chat_list = convertMessages(allMessages)
                chat_list = processMessages(chat_list)
                if (socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify({ chat_list, source, model }));
                } else {
                    // 可以在这里通知用户WebSocket未连接
                    $('#loginModal').modal('show')
                    console.error("连接失败，请刷新页面重试");
                    return
                }
                // 清除输入字段
                document.getElementById("user-question").value = "";
                // 滚动到聊天框的底部
                document.getElementById("chat-box").scrollTop = document.getElementById("chat-box").scrollHeight;

                const modelMessage = document.createElement("div");
                modelMessage.classList.add("message");
                modelMessage.innerHTML = "<p>Bot:</p> <div class='message-content'>" + "</div>";
                document.getElementById("chat-box").appendChild(modelMessage);
                Temp = modelMessage
                $("#submitBtn").prop("disabled", true);
            }
            textarea.style.height = '40px';
        }

        function handleKeyDown(event) {
            if (event.keyCode === 13 && !event.shiftKey) {
                event.preventDefault();
                submitQuestion();
            }
        }

        var textarea = document.getElementById('user-question');
        textarea.addEventListener('input', () => {
            // Reset the height to auto to get the correct scrollHeight
            textarea.style.height = '40px';

            // Get the scrollHeight and set it as the new height
            textarea.style.height = `${textarea.scrollHeight}px`;

            // If the new height exceeds max-height, set it back to max-height and allow scrolling
            const maxHeight = parseInt(window.getComputedStyle(textarea).maxHeight);
            if (textarea.scrollHeight > maxHeight) {
                textarea.style.height = `${maxHeight}px`;
            }
        });


        //发送聊天信息至后台
        socket.onmessage = function (event) {
            const data = JSON.parse(event.data);
            if (data["target"] === 4) {

                if ("message" in data) {
                    messageBuffer += data.message;
                    messageBuffer = messageBuffer.replace(/<br>/g, '\n');
                    Temp.innerHTML = "<p>Bot:</p> <div class='message-content'>" + detectAndHighlightCode(messageBuffer) + "</div>";
                }
                if ("status" in data) {
                    if (data.status === 1) {
                        console.log("完毕")
                    } else if (data.status === 0) {
                        console.log("服务器网络故障")
                    }
                    $("#submitBtn").prop("disabled", false);

                }
            }
        }

        //用于设置记忆的最大聊天轮数和字数
        function processMessages(messages, maxRounds = 10, maxChars = 80000) {
            // Ensure we have the latest maxRounds messages
            if (messages.length > maxRounds) {
                messages = messages.slice(-maxRounds);
            }

            // Calculate total character length of messages
            let totalLength = messages.reduce((acc, msg) => acc + msg.length, 0);

            // Remove oldest messages until total length is within the limit
            while (totalLength > maxChars && messages.length > 0) {
                messages.shift();
                totalLength = messages.reduce((acc, msg) => acc + msg.length, 0);
            }

            return messages;
        }

        //将聊天列表转化为user/assitant格式
        function convertMessages(rawMessages) {
            return rawMessages.map(message => {
                const role = message.startsWith('You:') ? 'user' : 'assistant';
                const content = message.replace(/^You: |^Bot: /, '').trim();
                return { role, content };
            });
        }







    </script>

</body>

</html>