Connection closed with code 1006
Received model: 
----------------------------------------------
Received text_data: {"chat_list":[{"role":"user","content":"nih"}],"source":4,"model":""}
----------------------------------------------
Received bytes_data: None
----------------------------------------------
received input_data：{'chat_list': [{'role': 'user', 'content': 'nih'}], 'source': 4, 'model': ''}
----------------------------------------------
Connection closed with code 1001
Received model: 
----------------------------------------------
Received text_data: {"chat_list":[{"role":"user","content":"你是什么模型呢"}],"source":4,"model":""}
----------------------------------------------
Received bytes_data: None
----------------------------------------------
received input_data：{'chat_list': [{'role': 'user', 'content': '你是什么模型呢'}], 'source': 4, 'model': ''}
----------------------------------------------
Connection closed with code 1006
Received model: gpt-4o-mini
----------------------------------------------
Received text_data: {"productIntroduction":"","model":"gpt-4o-mini","source":1,"outline":"文案大纲将在此处生成...","brand":"LV","platform":"小红书","copytheme":"","copytype":"New launch","ifuseexamples":0,"example1":"","example2":"","example3":"","copytheme2":"魅力四射","useContentDescription":false,"contentDescriptionText":"","productName":"路易威登爆香香水","productAudience":"爱美年轻女性","productSellingPoints":"吸引高富帅"}
----------------------------------------------
Received bytes_data: None
----------------------------------------------
received input_data：{'productIntroduction': '', 'model': 'gpt-4o-mini', 'source': 1, 'outline': '文案大纲将在此处生成...', 'brand': 'LV', 'platform': '小红书', 'copytheme': '', 'copytype': 'New launch', 'ifuseexamples': 0, 'example1': '', 'example2': '', 'example3': '', 'copytheme2': '魅力四射', 'useContentDescription': False, 'contentDescriptionText': '', 'productName': '路易威登爆香香水', 'productAudience': '爱美年轻女性', 'productSellingPoints': '吸引高富帅'}
----------------------------------------------
整合的content:  你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。
----------------------------------------------
message: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}]
----------------------------------------------
初始化模型： gpt-4o-mini, 反馈模型: gpt-4o
--------------------------------------------------------
Initial input data: {'productIntroduction': '', 'model': 'gpt-4o-mini', 'source': 1, 'outline': '文案大纲将在此处生成...', 'brand': 'LV', 'platform': '小红书', 'copytheme': '', 'copytype': 'New launch', 'ifuseexamples': 0, 'example1': '', 'example2': '', 'example3': '', 'copytheme2': '魅力四射', 'useContentDescription': False, 'contentDescriptionText': '', 'productName': '路易威登爆香香水', 'productAudience': '爱美年轻女性', 'productSellingPoints': '吸引高富帅'}
--------------------------------------------------------
initial_conversation_history: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}]
----------------------------------------------
Initial Response: ### 标题：✨魅力四射，捕获所有目光

### 正文：
🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅。  
🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力。  
👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点。  
✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采。
--------------------------------------------------------
initial_conversation_history:[{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '### 标题：✨魅力四射，捕获所有目光\n\n### 正文：\n🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅。  \n🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力。  \n👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点。  \n✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采。'}]
----------------------------------------------
feedback_prompt: 你是专业的文案审核员，负责从下面几个方面去检查工人创作的文案，并给出存在的问题。 #<检查方面> 1.创作句子不能是作文句式，需要符合品牌的调性；2.不能使用俗气、极端的词语；3.避免使用“完美”词汇；4.前后衔接需要符合逻辑；5.避免过度拟人化和不恰当的修辞使用；6.每句话不能过长。 #<输出格式>问题句子：存在问题 #<示例内容> 示例1：每一位佩戴者都能感受到如水般的柔美与坚韧:像写作文，不符合奢侈品内容调性; 示例2：象征着命运交汇的美丽旅程,每一件作品如同光芒四射的太阳，以至简之形涵养无尽情感:像写作文，不符合奢侈品内容调性。 示例3：共同谱写出不凡的时尚篇章：不高级、不符合奢侈品调性；CHAUMET致力于将每位佩戴者的真我加冕，激发出如水般灵动的女王气息：不高级、不符合奢侈品调性；在这个春日，让奢华与自信携手，绽放无尽魅力！：不高级、不符合奢侈品调性；每一款戒指均展现出独特“V形”设计，灵感源自历史经典，仿佛重现了约瑟芬皇后的华贵与诗意：前后文逻辑不紧密； 示例4：象征爱情稳固甜蜜：不高级、不符合奢侈品调性；钻石与黄金华丽交融：前后文逻辑不紧密； #下面是需要审核的句子：<### 标题：✨魅力四射，捕获所有目光

### 正文：
🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅。  
🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力。  
👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点。  
✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采。> 
--------------------------------------------------------
feedback_prompt_conversation_history: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '### 标题：✨魅力四射，捕获所有目光\n\n### 正文：\n🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅。  \n🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力。  \n👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点。  \n✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采。'}]
--------------------------------------------------------
Feedback result: 问题句子：✨魅力四射，捕获所有目光：不符合奢侈品调性，略显庸俗。

问题句子：🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅：使用了“爆香”这样的极端词语，不符合奢侈品调性。

问题句子：🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力：句子较长且“沁人心脾”的描述过于拟人化，略显俗套。

问题句子：👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点：过度拟人化，“仿佛是为每位年轻女性量身定制”的措辞不够高级。

问题句子：✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采：使用了“高富帅”这种俗气的词语，不符合奢侈品调性。
--------------------------------------------------------
final_prompt: 请结合优化意见进行修改。 #<需要修改的文案：### 标题：✨魅力四射，捕获所有目光

### 正文：
🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅。  
🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力。  
👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点。  
✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采。>。 #根据优化意见进行优化文案，但不能脱离产品真实性和品牌调性。 #<下面是优化意见：问题句子：✨魅力四射，捕获所有目光：不符合奢侈品调性，略显庸俗。

问题句子：🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅：使用了“爆香”这样的极端词语，不符合奢侈品调性。

问题句子：🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力：句子较长且“沁人心脾”的描述过于拟人化，略显俗套。

问题句子：👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点：过度拟人化，“仿佛是为每位年轻女性量身定制”的措辞不够高级。

问题句子：✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采：使用了“高富帅”这种俗气的词语，不符合奢侈品调性。>。 只需要输出最终文案：。 
--------------------------------------------------------
final_messages: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '### 标题：✨魅力四射，捕获所有目光\n\n### 正文：\n🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅。  \n🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力。  \n👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点。  \n✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采。'}, {'role': 'user', 'content': '请结合优化意见进行修改。 #<需要修改的文案：### 标题：✨魅力四射，捕获所有目光\n\n### 正文：\n🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅。  \n🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力。  \n👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点。  \n✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采。>。 #根据优化意见进行优化文案，但不能脱离产品真实性和品牌调性。 #<下面是优化意见：问题句子：✨魅力四射，捕获所有目光：不符合奢侈品调性，略显庸俗。\n\n问题句子：🌹邂逅路易威登爆香香水，感受无与伦比的魅力与优雅：使用了“爆香”这样的极端词语，不符合奢侈品调性。\n\n问题句子：🌬️清新的香氛如晨露般沁人心脾，融汇了柑橘与花朵的温柔气息，让每一个瞬间都散发自信与吸引力：句子较长且“沁人心脾”的描述过于拟人化，略显俗套。\n\n问题句子：👗仿佛是为每位年轻女性量身定制，这款香水让你在任何场合中，成为众人瞩目的焦点：过度拟人化，“仿佛是为每位年轻女性量身定制”的措辞不够高级。\n\n问题句子：✨挥洒自如，难以抗拒，与高富帅相伴，尽显独特风采：使用了“高富帅”这种俗气的词语，不符合奢侈品调性。>。 只需要输出最终文案：。 '}]
--------------------------------------------------------
Final Result: ### 标题：✨优雅蝶舞，魅力绽放

### 正文：
🌸 迎接路易威登爆香香水，以其独特的气息，展现令人无法抗拒的优雅风姿。  
🌿这一款香水融合清新的柑橘香调与轻柔花香，轻轻缭绕，仿佛唤醒每一个感官，点亮你的每一天。  
💫 专为追求精致生活的年轻女性设计，在每一个重要时刻都尽显迷人气质。  
🌟 让自信从此伴随你左右，个性鲜明，绽放无尽魅力。
--------------------------------------------------------
Connection closed with code 1001
Connection closed with code 1006
Received model: gpt-4o-mini
----------------------------------------------
Received text_data: {"productIntroduction":"","model":"gpt-4o-mini","source":1,"outline":"文案大纲将在此处生成...","brand":"LV","platform":"小红书","copytheme":"","copytype":"New launch","ifuseexamples":0,"example1":"","example2":"","example3":"","copytheme2":"魅力四射","useContentDescription":false,"contentDescriptionText":"","productName":"路易威登爆香香水","productAudience":"爱美年轻女性","productSellingPoints":"吸引高富帅"}
----------------------------------------------
Received bytes_data: None
----------------------------------------------
received input_data：{'productIntroduction': '', 'model': 'gpt-4o-mini', 'source': 1, 'outline': '文案大纲将在此处生成...', 'brand': 'LV', 'platform': '小红书', 'copytheme': '', 'copytype': 'New launch', 'ifuseexamples': 0, 'example1': '', 'example2': '', 'example3': '', 'copytheme2': '魅力四射', 'useContentDescription': False, 'contentDescriptionText': '', 'productName': '路易威登爆香香水', 'productAudience': '爱美年轻女性', 'productSellingPoints': '吸引高富帅'}
----------------------------------------------
整合的content:  你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。
----------------------------------------------
message: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}]
----------------------------------------------
初始化模型： gpt-4o-mini, 反馈模型: gpt-4o
--------------------------------------------------------
Initial input data: {'productIntroduction': '', 'model': 'gpt-4o-mini', 'source': 1, 'outline': '文案大纲将在此处生成...', 'brand': 'LV', 'platform': '小红书', 'copytheme': '', 'copytype': 'New launch', 'ifuseexamples': 0, 'example1': '', 'example2': '', 'example3': '', 'copytheme2': '魅力四射', 'useContentDescription': False, 'contentDescriptionText': '', 'productName': '路易威登爆香香水', 'productAudience': '爱美年轻女性', 'productSellingPoints': '吸引高富帅'}
--------------------------------------------------------
initial_conversation_history: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}]
----------------------------------------------
Initial Response: ### 标题：✨魅力四射，绽放无尽吸引力

### 正文：
💖 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。  
🍃 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。  
🌸 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。  
💎 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。
--------------------------------------------------------
initial_conversation_history:[{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '### 标题：✨魅力四射，绽放无尽吸引力\n\n### 正文：\n💖 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。  \n🍃 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。  \n🌸 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。  \n💎 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。'}]
----------------------------------------------
feedback_prompt: 你是专业的文案审核员，负责从下面几个方面去检查工人创作的文案，并给出存在的问题。 #<检查方面> 1.创作句子不能是作文句式，需要符合品牌的调性；2.不能使用俗气、极端的词语；3.避免使用“完美”词汇；4.前后衔接需要符合逻辑；5.避免过度拟人化和不恰当的修辞使用；6.每句话不能过长。 #<输出格式>问题句子：存在问题 #<示例内容> 示例1：每一位佩戴者都能感受到如水般的柔美与坚韧:像写作文，不符合奢侈品内容调性; 示例2：象征着命运交汇的美丽旅程,每一件作品如同光芒四射的太阳，以至简之形涵养无尽情感:像写作文，不符合奢侈品内容调性。 示例3：共同谱写出不凡的时尚篇章：不高级、不符合奢侈品调性；CHAUMET致力于将每位佩戴者的真我加冕，激发出如水般灵动的女王气息：不高级、不符合奢侈品调性；在这个春日，让奢华与自信携手，绽放无尽魅力！：不高级、不符合奢侈品调性；每一款戒指均展现出独特“V形”设计，灵感源自历史经典，仿佛重现了约瑟芬皇后的华贵与诗意：前后文逻辑不紧密； 示例4：象征爱情稳固甜蜜：不高级、不符合奢侈品调性；钻石与黄金华丽交融：前后文逻辑不紧密； #下面是需要审核的句子：<### 标题：✨魅力四射，绽放无尽吸引力

### 正文：
💖 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。  
🍃 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。  
🌸 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。  
💎 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。> 
--------------------------------------------------------
feedback_prompt_conversation_history: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '### 标题：✨魅力四射，绽放无尽吸引力\n\n### 正文：\n💖 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。  \n🍃 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。  \n🌸 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。  \n💎 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。'}]
--------------------------------------------------------
Feedback result: ### 标题：✨魅力四射，绽放无尽吸引力

### 正文：
1. 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。
    - 存在问题：像写作文，不符合奢侈品内容调性；使用“璀璨夺目”过于追求极端效果。

2. 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。
    - 存在问题：拟人化修辞不当（“轻柔的晨曦唤醒感官”）。

3. 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。
    - 存在问题：像写作文，不符合奢侈品内容调性；句子过长。

4. 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。
    - 存在问题：使用“高富帅”俗气且不符合品牌调性；“谱写迷人的旅行篇章”像写作文，不符合奢侈品内容调性。
--------------------------------------------------------
final_prompt: 请结合优化意见进行修改。 #<需要修改的文案：### 标题：✨魅力四射，绽放无尽吸引力

### 正文：
💖 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。  
🍃 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。  
🌸 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。  
💎 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。>。 #根据优化意见进行优化文案，但不能脱离产品真实性和品牌调性。 #<下面是优化意见：### 标题：✨魅力四射，绽放无尽吸引力

### 正文：
1. 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。
    - 存在问题：像写作文，不符合奢侈品内容调性；使用“璀璨夺目”过于追求极端效果。

2. 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。
    - 存在问题：拟人化修辞不当（“轻柔的晨曦唤醒感官”）。

3. 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。
    - 存在问题：像写作文，不符合奢侈品内容调性；句子过长。

4. 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。
    - 存在问题：使用“高富帅”俗气且不符合品牌调性；“谱写迷人的旅行篇章”像写作文，不符合奢侈品内容调性。>。 只需要输出最终文案：。 
--------------------------------------------------------
final_messages: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '### 标题：✨魅力四射，绽放无尽吸引力\n\n### 正文：\n💖 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。  \n🍃 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。  \n🌸 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。  \n💎 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。'}, {'role': 'user', 'content': '请结合优化意见进行修改。 #<需要修改的文案：### 标题：✨魅力四射，绽放无尽吸引力\n\n### 正文：\n💖 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。  \n🍃 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。  \n🌸 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。  \n💎 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。>。 #根据优化意见进行优化文案，但不能脱离产品真实性和品牌调性。 #<下面是优化意见：### 标题：✨魅力四射，绽放无尽吸引力\n\n### 正文：\n1. 路易威登爆香香水，犹如迷人法宝，令每个瞬间都璀璨夺目。\n    - 存在问题：像写作文，不符合奢侈品内容调性；使用“璀璨夺目”过于追求极端效果。\n\n2. 清新香调蕴含柑橘与葛缕子，仿佛轻柔的晨曦瞬间唤醒感官，勾勒出独特的个人魅力。\n    - 存在问题：拟人化修辞不当（“轻柔的晨曦唤醒感官”）。\n\n3. 随着时间的推移，花卉香气渐渐渗透，缠绕于心，令你在每次出行中成为众人瞩目的焦点。\n    - 存在问题：像写作文，不符合奢侈品内容调性；句子过长。\n\n4. 此刻，释放自信，纵享每一次邂逅的可能，吸引真正的高富帅，谱写迷人的旅行篇章。\n    - 存在问题：使用“高富帅”俗气且不符合品牌调性；“谱写迷人的旅行篇章”像写作文，不符合奢侈品内容调性。>。 只需要输出最终文案：。 '}]
--------------------------------------------------------
Final Result: ### 标题：✨魅力四射，点亮你的优雅瞬间

### 正文：
💖 路易威登爆香香水，赋予每位女性独特芬芳，让你在众人中脱颖而出。  
🍃 清新明亮的香调交融柑橘与葛缕子，唤醒感官，散发出若隐若现的精致魅力。  
🌸 随着时间的流逝，绽放出的花卉香气如丝般缠绕，仿佛在诉说每一个迷人故事。  
💎 每次出门，尽情散发自信与优雅，构筑属于你的迷人魅力之旅。
--------------------------------------------------------
2025-06-19 14:28:28,749 - daphne.server - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-19 14:28:28,749 - daphne.server - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-19 14:28:28,750 - daphne.server - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-19 14:28:29,524 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime **********.321
2025-06-19 14:28:53,246 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py previous mtime: 1750314501.9996438, current mtime: 1750314532.884717
2025-06-19 14:28:53,246 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py notified as changed. Signal results: [(<function template_changed at 0x000001863DF74900>, None), (<function translation_file_changed at 0x0000018640D33600>, None)].
2025-06-19 14:28:53,247 - django.utils.autoreload - INFO - D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py changed, reloading.
2025-06-19 14:28:58,439 - daphne.server - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-19 14:28:58,439 - daphne.server - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-19 14:28:58,440 - daphne.server - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-19 14:28:59,437 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime **********.321
2025-06-19 14:30:11,883 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py previous mtime: 1750314532.884717, current mtime: 1750314611.1923082
2025-06-19 14:30:11,883 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py notified as changed. Signal results: [(<function template_changed at 0x0000027353F04900>, None), (<function translation_file_changed at 0x0000027356CC3600>, None)].
2025-06-19 14:30:11,883 - django.utils.autoreload - INFO - D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py changed, reloading.
2025-06-19 14:30:17,382 - daphne.server - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-19 14:30:17,382 - daphne.server - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-19 14:30:17,383 - daphne.server - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-19 14:30:17,916 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 14:30:17,917 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 14:30:17,919 - django.db.backends - DEBUG - (0.015) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 06:30:17.910866' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 06:30:17.910866', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 14:30:17,921 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 14:30:17,924 - django.channels.server - INFO - HTTP GET / 200 [0.03, 127.0.0.1:49966]
2025-06-19 14:30:18,195 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime **********.321
2025-06-19 14:30:18,278 - django.channels.server - WARNING - HTTP GET /static/pic/Wilson_Blade_v9.png 404 [0.01, 127.0.0.1:49966]
2025-06-19 14:30:18,280 - django.channels.server - INFO - WebSocket HANDSHAKING /ws/gpt/ [127.0.0.1:49969]
2025-06-19 14:30:18,284 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 14:30:18,285 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 14:30:18,286 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 06:30:18.281625' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 06:30:18.281625', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 14:30:18,287 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 14:30:18,289 - django.channels.server - INFO - WebSocket CONNECT /ws/gpt/ [127.0.0.1:49969]
2025-06-19 14:30:19,616 - django.channels.server - INFO - HTTP GET /copywritting/ 200 [0.01, 127.0.0.1:49966]
2025-06-19 14:30:19,761 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\context_processors.py first seen with mtime **********.5782743
2025-06-19 14:30:19,781 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\fallback.py first seen with mtime **********.1464849
2025-06-19 14:30:19,782 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\staticfiles\storage.py first seen with mtime **********.5785098
2025-06-19 14:30:19,794 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\cookie.py first seen with mtime **********.1464849
2025-06-19 14:30:19,801 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\serializers.py first seen with mtime **********.3189974
2025-06-19 14:30:19,806 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\context_processors.py first seen with mtime **********.1434848
2025-06-19 14:30:19,816 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\template\context_processors.py first seen with mtime **********.7127118
2025-06-19 14:30:19,822 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\session.py first seen with mtime **********.1464849
2025-06-19 14:30:28,228 - style_content.consumers - INFO - Received model: gpt-3.5-turbo
2025-06-19 14:30:28,228 - style_content.consumers - INFO - ----------------------------------------------
2025-06-19 14:30:28,229 - style_content.consumers - INFO - Received text_data: {"productIntroduction":"","model":"gpt-3.5-turbo","source":1,"outline":"文案大纲将在此处生成...","brand":"LV","platform":"小红书","copytheme":"","copytype":"New launch","ifuseexamples":0,"example1":"","example2":"","example3":"","copytheme2":"魅力四射","useContentDescription":false,"contentDescriptionText":"","productName":"路易威登爆香香水","productAudience":"爱美年轻女性","productSellingPoints":"吸引高富帅"}
2025-06-19 14:30:28,229 - style_content.consumers - INFO - ----------------------------------------------
2025-06-19 14:30:28,229 - style_content.consumers - INFO - Received input_data: {'productIntroduction': '', 'model': 'gpt-3.5-turbo', 'source': 1, 'outline': '文案大纲将在此处生成...', 'brand': 'LV', 'platform': '小红书', 'copytheme': '', 'copytype': 'New launch', 'ifuseexamples': 0, 'example1': '', 'example2': '', 'example3': '', 'copytheme2': '魅力四射', 'useContentDescription': False, 'contentDescriptionText': '', 'productName': '路易威登爆香香水', 'productAudience': '爱美年轻女性', 'productSellingPoints': '吸引高富帅'}
整合的content:  你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。
----------------------------------------------
message: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}]
----------------------------------------------
初始化模型： gpt-3.5-turbo, 反馈模型: gpt-4o
--------------------------------------------------------
Initial input data: {'productIntroduction': '', 'model': 'gpt-3.5-turbo', 'source': 1, 'outline': '文案大纲将在此处生成...', 'brand': 'LV', 'platform': '小红书', 'copytheme': '', 'copytype': 'New launch', 'ifuseexamples': 0, 'example1': '', 'example2': '', 'example3': '', 'copytheme2': '魅力四射', 'useContentDescription': False, 'contentDescriptionText': '', 'productName': '路易威登爆香香水', 'productAudience': '爱美年轻女性', 'productSellingPoints': '吸引高富帅'}
--------------------------------------------------------
initial_conversation_history: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}]
2025-06-19 14:30:28,934 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\encodings\idna.py first seen with mtime **********.0
2025-06-19 14:30:28,959 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\stringprep.py first seen with mtime **********.0
2025-06-19 14:30:28,963 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_backends\__init__.py first seen with mtime **********.6711361
2025-06-19 14:30:29,011 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_backends\_asyncio.py first seen with mtime **********.6721363
2025-06-19 14:30:31,214 - httpx - INFO - HTTP Request: POST https://xiaoai.plus/v1/chat/completions "HTTP/1.1 200 OK"
----------------------------------------------
Initial Response: #魅力四射，路易威登爆香香水让你成为焦点✨

迷人的香氛，是你吸引高富帅注目的秘密武器。路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉。每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦。投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨。
--------------------------------------------------------
initial_conversation_history:[{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '#魅力四射，路易威登爆香香水让你成为焦点✨\n\n迷人的香氛，是你吸引高富帅注目的秘密武器。路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉。每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦。投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨。'}]
----------------------------------------------
feedback_prompt: 你是专业的文案审核员，负责从下面几个方面去检查工人创作的文案，并给出存在的问题。 #<检查方面> 1.创作句子不能是作文句式，需要符合品牌的调性；2.不能使用俗气、极端的词语；3.避免使用“完美”词汇；4.前后衔接需要符合逻辑；5.避免过度拟人化和不恰当的修辞使用；6.每句话不能过长。 #<输出格式>问题句子：存在问题 #<示例内容> 示例1：每一位佩戴者都能感受到如水般的柔美与坚韧:像写作文，不符合奢侈品内容调性; 示例2：象征着命运交汇的美丽旅程,每一件作品如同光芒四射的太阳，以至简之形涵养无尽情感:像写作文，不符合奢侈品内容调性。 示例3：共同谱写出不凡的时尚篇章：不高级、不符合奢侈品调性；CHAUMET致力于将每位佩戴者的真我加冕，激发出如水般灵动的女王气息：不高级、不符合奢侈品调性；在这个春日，让奢华与自信携手，绽放无尽魅力！：不高级、不符合奢侈品调性；每一款戒指均展现出独特“V形”设计，灵感源自历史经典，仿佛重现了约瑟芬皇后的华贵与诗意：前后文逻辑不紧密； 示例4：象征爱情稳固甜蜜：不高级、不符合奢侈品调性；钻石与黄金华丽交融：前后文逻辑不紧密； #下面是需要审核的句子：<#魅力四射，路易威登爆香香水让你成为焦点✨

迷人的香氛，是你吸引高富帅注目的秘密武器。路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉。每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦。投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨。> 
--------------------------------------------------------
feedback_prompt_conversation_history: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '#魅力四射，路易威登爆香香水让你成为焦点✨\n\n迷人的香氛，是你吸引高富帅注目的秘密武器。路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉。每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦。投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨。'}]
--------------------------------------------------------
2025-06-19 14:30:34,003 - httpx - INFO - HTTP Request: POST https://xiaoai.plus/v1/chat/completions "HTTP/1.1 200 OK"
Feedback result: 1. 魅力四射，路易威登爆香香水让你成为焦点✨：不符合品牌调性，表达过于俗气。
2. 迷人的香氛，是你吸引高富帅注目的秘密武器：不符合品牌调性，表达不够高级，使用了俗气的词语“高富帅”。
3. 路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉：句子过长，并且表达不够高级。
4. 每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦：过度拟人化，“诉说着你的光芒万丈”不符合品牌调性。
5. 投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨：句子过长，使用了“不恰当的修辞”。
--------------------------------------------------------
final_prompt: 请结合优化意见进行修改。 #<需要修改的文案：#魅力四射，路易威登爆香香水让你成为焦点✨

迷人的香氛，是你吸引高富帅注目的秘密武器。路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉。每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦。投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨。>。 #根据优化意见进行优化文案，但不能脱离产品真实性和品牌调性。 #<下面是优化意见：1. 魅力四射，路易威登爆香香水让你成为焦点✨：不符合品牌调性，表达过于俗气。
2. 迷人的香氛，是你吸引高富帅注目的秘密武器：不符合品牌调性，表达不够高级，使用了俗气的词语“高富帅”。
3. 路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉：句子过长，并且表达不够高级。
4. 每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦：过度拟人化，“诉说着你的光芒万丈”不符合品牌调性。
5. 投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨：句子过长，使用了“不恰当的修辞”。>。 只需要输出最终文案：。 
2025-06-19 14:30:36,462 - httpx - INFO - HTTP Request: POST https://xiaoai.plus/v1/chat/completions "HTTP/1.1 200 OK"
--------------------------------------------------------
final_messages: [{'role': 'system', 'content': '你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作： #小红书文案编辑专家的任务 ##根据提供的信息：产品资料、产品受众、产品卖点和文案主题，充分理解并分析品牌的调性、产品的特点和卖点，为{产品名称}生成一条标题和相应的正文文案。 # 品牌文案用词偏好 - 品牌关键词：“旅行”、“旅行精神” - 女装风格：现代摩登、前卫感 - 男装风格：潮流时尚，避免街头风，保持高级感 - 香水类产品：巧妙融入材料特点，营造浪漫气息 - 珠宝、腕表等产品类的文案：注重高级感，呈现奢华精致 # 文案生成规范 ## 1. 文案目标： - 突出主推产品的核心卖点、产品特色。 ## 2. 文案注意事项： - 文案需逻辑清晰、行文流畅，句子前后有节奏感。 - 用词精准易懂，避免使用极端或不符合品牌形象的词汇。 - 基于事实，避免虚构内容，同时通过合理的场景化塑造增强文案的吸引力，令读者产生画面感。 ## 3. 文案风格： - 必须仔细分析以下示例中的文案风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、句式结构（短句、排比），并据此创作与品牌调性高度一致的文案。 ## 示例 ### 珠宝系列示例1： - 标题：✨光芒熠动，成就万能叠戴法则 - 正文： 路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。 ��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。 ��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。 ### 女装系列示例2： - 标题：⚓️赴约海滨假日 - 正文： ��海风轻拂，纵享感官假日。 ��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。 白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。 ��揭晓 GO-14 手袋美妙新色，续写愉悦心绪； Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。 ### 男装系列示例3： - 标题：领取你的海岛 OOTD - 正文： ��登陆灵感航线，点亮造型意趣。 路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。 ��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。 ### 香水系列示例4： - 标题：寻迹芳香，步入追梦之旅 - 正文： ��邂逅梦幻香息，传递恒久爱意。 ��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。 ��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。 ��携芬芳之礼传递心意，捕捉心动频率 # 文案生成流程 1. 理解品牌与产品：分析品牌调性、明确目标受众需求。 2. 提炼卖点：提取独特卖点，确保传达品牌高级感。 3. 标题创作：标题简洁准备、有吸引力，突出产品核心卖点。 4. 正文撰写：3-5句内容，简洁流畅，符合品牌风格，，适量使用emoji。 5. 校对优化：确保文案符合所有要求，进行必要的修改。'}, {'role': 'user', 'content': ' #产品名称：路易威登爆香香水 #产品资料： #产品受众：爱美年轻女性 #产品卖点：吸引高富帅 #文案主题：魅力四射  '}, {'role': 'assistant', 'content': '#魅力四射，路易威登爆香香水让你成为焦点✨\n\n迷人的香氛，是你吸引高富帅注目的秘密武器。路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉。每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦。投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨。'}, {'role': 'user', 'content': '请结合优化意见进行修改。 #<需要修改的文案：#魅力四射，路易威登爆香香水让你成为焦点✨\n\n迷人的香氛，是你吸引高富帅注目的秘密武器。路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉。每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦。投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨。>。 #根据优化意见进行优化文案，但不能脱离产品真实性和品牌调性。 #<下面是优化意见：1. 魅力四射，路易威登爆香香水让你成为焦点✨：不符合品牌调性，表达过于俗气。\n2. 迷人的香氛，是你吸引高富帅注目的秘密武器：不符合品牌调性，表达不够高级，使用了俗气的词语“高富帅”。\n3. 路易威登爆香香水，散发着独特的魅力，让你在人群中熠熠生辉：句子过长，并且表达不够高级。\n4. 每一滴香水都仿佛诉说着你的光芒万丈，吸引所有目光的聚焦：过度拟人化，“诉说着你的光芒万丈”不符合品牌调性。\n5. 投射出自信、优雅和神秘感，让你成为场上的明星，引领潮流，成为身边所有人羡慕的焦点✨：句子过长，使用了“不恰当的修辞”。>。 只需要输出最终文案：。 '}]
--------------------------------------------------------
Final Result: #路易威登爆香香水，唤醒你的独特魅力✨

悠扬的香调，独具魅力，让你无需言语即能吸引所有眼光。路易威登爆香香水，散发着独特的馥郁芬芳，像是为你量身定制的魅力符号。自信、优雅、神秘，如影随形般展现，让你成为聚光灯下的耀眼明星，引领时尚风潮，成为羡慕的焦点✨。
--------------------------------------------------------
2025-06-19 14:30:37,069 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\ssl_.py first seen with mtime 1742633606.85191
2025-06-19 14:30:37,070 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\md__mypyc.cp311-win_amd64.pyd first seen with mtime **********.371781
2025-06-19 14:30:37,071 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\url.py first seen with mtime 1742633606.8539102
2025-06-19 14:30:37,072 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\connection.py first seen with mtime 1742633606.8419101
2025-06-19 14:30:37,076 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\__init__.py first seen with mtime **********.3677852
2025-06-19 14:30:37,077 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\load.py first seen with mtime **********.9815776
2025-06-19 14:30:37,078 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\timeout.py first seen with mtime 1742633606.85291
2025-06-19 14:30:37,080 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\models.py first seen with mtime **********.371781
2025-06-19 14:30:37,084 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\version.py first seen with mtime **********.3727813
2025-06-19 14:30:37,085 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\md.cp311-win_amd64.pyd first seen with mtime **********.369781
2025-06-19 14:30:37,085 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\response.py first seen with mtime 1742633606.8449101
2025-06-19 14:30:37,086 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\simplejson\errors.py first seen with mtime 1742638005.1063356
2025-06-19 14:30:37,090 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\__init__.py first seen with mtime 1742633606.8399143
2025-06-19 14:30:37,090 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\proxy.py first seen with mtime 1742633606.8499103
2025-06-19 14:30:37,091 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\_request_methods.py first seen with mtime 1742633606.8409102
2025-06-19 14:30:37,094 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\exceptions.py first seen with mtime **********.491253
2025-06-19 14:30:37,096 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\simplejson\decoder.py first seen with mtime 1742638005.1053436
2025-06-19 14:30:37,096 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\util.py first seen with mtime 1742633606.8539102
2025-06-19 14:30:37,097 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\_collections.py first seen with mtime 1742633606.8409102
2025-06-19 14:30:37,098 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\connection.py first seen with mtime 1742633606.8499103
2025-06-19 14:30:37,099 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\compat.py first seen with mtime **********.490253
2025-06-19 14:30:37,101 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\constant.py first seen with mtime **********.369781
2025-06-19 14:30:37,104 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\connectionpool.py first seen with mtime 1742633606.8419101
2025-06-19 14:30:37,106 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\request.py first seen with mtime 1742633606.8499103
2025-06-19 14:30:37,109 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\fields.py first seen with mtime 1742633606.8429103
2025-06-19 14:30:37,111 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\simplejson\scanner.py first seen with mtime 1742638005.1073346
2025-06-19 14:30:37,114 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\simplejson\__init__.py first seen with mtime 1742638005.1043346
2025-06-19 14:30:37,114 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\response.py first seen with mtime 1742633606.8509102
2025-06-19 14:30:37,115 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\http2\__init__.py first seen with mtime 1742633606.8479104
2025-06-19 14:30:37,117 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\__init__.py first seen with mtime 1742633606.84891
2025-06-19 14:30:37,121 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\exceptions.py first seen with mtime 1742633606.8429103
2025-06-19 14:30:37,124 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\legacy.py first seen with mtime **********.369781
2025-06-19 14:30:37,125 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\filepost.py first seen with mtime 1742633606.8439102
2025-06-19 14:30:37,126 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\ssltransport.py first seen with mtime 1742633606.85191
2025-06-19 14:30:37,126 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\ssl_match_hostname.py first seen with mtime 1742633606.85191
2025-06-19 14:30:37,127 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\utils.py first seen with mtime **********.3727813
2025-06-19 14:30:37,132 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\cd.py first seen with mtime **********.368781
2025-06-19 14:30:37,134 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\poolmanager.py first seen with mtime 1742633606.8439102
2025-06-19 14:30:37,137 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\http2\probe.py first seen with mtime 1742633606.84891
2025-06-19 14:30:37,140 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\simplejson\raw_json.py first seen with mtime 1742638005.1073346
2025-06-19 14:30:37,141 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\wait.py first seen with mtime 1742633606.8539102
2025-06-19 14:30:37,142 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\_version.py first seen with mtime 1742633606.8419101
2025-06-19 14:30:37,144 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken_ext\openai_public.py first seen with mtime **********.9839046
2025-06-19 14:30:37,145 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\simplejson\compat.py first seen with mtime 1742638005.1053436
2025-06-19 14:30:37,152 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\_base_connection.py first seen with mtime 1742633606.8399143
2025-06-19 14:30:37,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\charset_normalizer\api.py first seen with mtime **********.368781
2025-06-19 14:30:37,161 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\util\retry.py first seen with mtime 1742633606.8509102
2025-06-19 14:30:37,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\__init__.py first seen with mtime **********.487257
2025-06-19 14:30:38,654 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\cookies.py first seen with mtime **********.490253
2025-06-19 14:30:38,676 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\netrc.py first seen with mtime **********.0
2025-06-19 14:30:38,689 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\utils.py first seen with mtime **********.493253
2025-06-19 14:30:38,699 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\adapters.py first seen with mtime **********.4882529
2025-06-19 14:30:38,701 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\sessions.py first seen with mtime **********.4922528
2025-06-19 14:30:38,703 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\simplejson\encoder.py first seen with mtime 1742638005.1063356
2025-06-19 14:30:38,708 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\certs.py first seen with mtime **********.4892528
2025-06-19 14:30:38,710 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\auth.py first seen with mtime **********.4892528
2025-06-19 14:30:38,713 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\simplejson\_speedups.cp311-win_amd64.pyd first seen with mtime 1742638005.1043346
2025-06-19 14:30:38,718 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\api.py first seen with mtime **********.4892528
2025-06-19 14:30:38,720 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\_internal_utils.py first seen with mtime **********.4882529
2025-06-19 14:30:38,723 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\hooks.py first seen with mtime **********.491253
2025-06-19 14:30:38,726 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\structures.py first seen with mtime **********.493253
2025-06-19 14:30:38,727 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\urllib3\contrib\__init__.py first seen with mtime 1742633606.8449101
2025-06-19 14:30:38,734 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\__version__.py first seen with mtime **********.4882529
2025-06-19 14:30:38,739 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\status_codes.py first seen with mtime **********.4922528
2025-06-19 14:30:38,742 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\packages.py first seen with mtime **********.4922528
2025-06-19 14:30:38,745 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\requests\models.py first seen with mtime **********.4922528
2025-06-19 14:30:39,475 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 14:30:39,476 - django.db.backends - DEBUG - (0.000) SELECT `accounts_usage`.`id`, `accounts_usage`.`userid_id`, `accounts_usage`.`use_time`, `accounts_usage`.`prompt_token`, `accounts_usage`.`output_token`, `accounts_usage`.`use_model` FROM `accounts_usage` WHERE (`accounts_usage`.`use_model` = 'gpt-3.5-turbo' AND `accounts_usage`.`use_time` = '2025-06-19' AND `accounts_usage`.`userid_id` = 2) ORDER BY `accounts_usage`.`id` ASC LIMIT 1; args=('gpt-3.5-turbo', '2025-06-19', 2); alias=default
2025-06-19 14:30:39,488 - django.db.backends - DEBUG - (0.016) INSERT INTO `accounts_usage` (`userid_id`, `use_time`, `prompt_token`, `output_token`, `use_model`) VALUES (2, '2025-06-19', 4824, 186, 'gpt-3.5-turbo'); args=[2, '2025-06-19', 4824, 186, 'gpt-3.5-turbo']; alias=default
2025-06-19 14:30:39,490 - django.db.backends - DEBUG - (0.000) SELECT COUNT(*) AS `__count` FROM `style_content_createdcontent` WHERE `style_content_createdcontent`.`userid_id` = 2; args=(2,); alias=default
2025-06-19 14:30:39,502 - django.db.backends - DEBUG - (0.016) INSERT INTO `style_content_createdcontent` (`userid_id`, `create_time`, `product_information`, `Outline`, `Final_output`) VALUES (2, '2025-06-19 14:30:39.488101', '', '文案大纲将在此处生成...', '#路易威登爆香香水，唤醒你的独特魅力✨\n\n悠扬的香调，独具魅力，让你无需言语即能吸引所有眼光。路易威登爆香香水，散发着独特的馥郁芬芳，像是为你量身定制的魅力符号。自信、优雅、神秘，如影随形般展现，让你成为聚光灯下的耀眼明星，引领时尚风潮，成为羡慕的焦点✨。'); args=[2, '2025-06-19 14:30:39.488101', '', '文案大纲将在此处生成...', '#路易威登爆香香水，唤醒你的独特魅力✨\n\n悠扬的香调，独具魅力，让你无需言语即能吸引所有眼光。路易威登爆香香水，散发着独特的馥郁芬芳，像是为你量身定制的魅力符号。自信、优雅、神秘，如影随形般展现，让你成为聚光灯下的耀眼明星，引领时尚风潮，成为羡慕的焦点✨。']; alias=default
2025-06-19 14:30:40,203 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tzdata\zoneinfo\__init__.py first seen with mtime 1742633469.7543924
2025-06-19 14:30:40,212 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tzdata\__init__.py first seen with mtime 1742633469.740393
2025-06-19 14:31:33,196 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 14:31:33,196 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 14:31:33,197 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 06:31:33.165108' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 06:31:33.165108', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 14:31:33,198 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 14:31:34,823 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-19 14:31:44,724 - django.channels.server - INFO - HTTP POST /productinfo/ 200 [11.56, 127.0.0.1:50033]
2025-06-19 14:47:09,487 - django.channels.server - INFO - HTTP GET /chat/ 200 [0.01, 127.0.0.1:50612]
2025-06-19 14:53:01,402 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index.zip first seen with mtime 1750315980.9271
2025-06-19 15:00:23,006 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1733578446.0, current mtime: 1750316422.955526
2025-06-19 15:00:23,091 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000021187584900>, True), (<function translation_file_changed at 0x000002118A343600>, None)].
2025-06-19 15:00:25,269 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750316422.955526, current mtime: 1750316424.4452786
2025-06-19 15:00:25,272 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000021187584900>, True), (<function translation_file_changed at 0x000002118A343600>, None)].
2025-06-19 15:00:28,522 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750316424.4452786, current mtime: 1750316427.4607756
2025-06-19 15:00:28,523 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000021187584900>, True), (<function translation_file_changed at 0x000002118A343600>, None)].
2025-06-19 15:00:42,099 - django.channels.server - INFO - WebSocket DISCONNECT /ws/gpt/ [127.0.0.1:49969]
Connection closed with code 1001
2025-06-19 15:02:30,888 - daphne.server - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-19 15:02:30,888 - daphne.server - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-19 15:02:30,889 - daphne.server - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-19 15:02:31,150 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:02:31,151 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:02:31,156 - django.db.backends - DEBUG - (0.016) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:02:31.137967' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:02:31.137967', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:02:31,159 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:02:31,161 - django.channels.server - INFO - HTTP GET / 200 [0.04, 127.0.0.1:51909]
2025-06-19 15:02:31,573 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime **********.321
2025-06-19 15:02:31,648 - django.channels.server - WARNING - HTTP GET /static/pic/Wilson_Blade_v9.png 404 [0.01, 127.0.0.1:51909]
2025-06-19 15:02:31,649 - django.channels.server - INFO - WebSocket HANDSHAKING /ws/gpt/ [127.0.0.1:51912]
2025-06-19 15:02:31,661 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:02:31,661 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:02:31,662 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:02:31.650694' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:02:31.650694', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:02:31,663 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:02:31,665 - django.channels.server - INFO - WebSocket CONNECT /ws/gpt/ [127.0.0.1:51912]
2025-06-19 15:02:32,959 - django.channels.server - INFO - HTTP GET /copywritting/ 200 [0.00, 127.0.0.1:51909]
2025-06-19 15:02:33,093 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\cookie.py first seen with mtime **********.1464849
2025-06-19 15:02:33,094 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\fallback.py first seen with mtime **********.1464849
2025-06-19 15:02:33,097 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\session.py first seen with mtime **********.1464849
2025-06-19 15:02:33,111 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\context_processors.py first seen with mtime **********.1434848
2025-06-19 15:02:33,113 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\template\context_processors.py first seen with mtime **********.7127118
2025-06-19 15:02:33,120 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\context_processors.py first seen with mtime **********.5782743
2025-06-19 15:02:33,134 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\serializers.py first seen with mtime **********.3189974
2025-06-19 15:02:33,139 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\staticfiles\storage.py first seen with mtime **********.5785098
2025-06-19 15:04:35,371 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py previous mtime: 1750314611.1923082, current mtime: 1750316675.2093546
2025-06-19 15:04:35,371 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py notified as changed. Signal results: [(<function template_changed at 0x000001B0F3F74900>, None), (<function translation_file_changed at 0x000001B0F6D33600>, None)].
2025-06-19 15:04:35,371 - django.utils.autoreload - INFO - D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py changed, reloading.
2025-06-19 15:04:41,362 - daphne.server - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-19 15:04:41,363 - daphne.server - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-19 15:04:41,363 - daphne.server - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-19 15:04:42,036 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime **********.321
2025-06-19 15:04:42,174 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:04:42,175 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:04:42,179 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:04:42.165627' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:04:42.165627', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:04:42,181 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:04:42,183 - django.channels.server - INFO - HTTP GET / 200 [0.03, 127.0.0.1:52074]
2025-06-19 15:04:42,542 - django.channels.server - WARNING - HTTP GET /static/pic/Wilson_Blade_v9.png 404 [0.01, 127.0.0.1:52074]
2025-06-19 15:04:42,555 - django.channels.server - INFO - WebSocket HANDSHAKING /ws/gpt/ [127.0.0.1:52077]
2025-06-19 15:04:42,563 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:04:42,563 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:04:42,565 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:04:42.556471' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:04:42.556471', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:04:42,567 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:04:42,569 - django.channels.server - INFO - WebSocket CONNECT /ws/gpt/ [127.0.0.1:52077]
2025-06-19 15:04:43,479 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\context_processors.py first seen with mtime **********.1434848
2025-06-19 15:04:43,488 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\session.py first seen with mtime **********.1464849
2025-06-19 15:04:43,499 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\context_processors.py first seen with mtime **********.5782743
2025-06-19 15:04:43,500 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\staticfiles\storage.py first seen with mtime **********.5785098
2025-06-19 15:04:43,506 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\template\context_processors.py first seen with mtime **********.7127118
2025-06-19 15:04:43,517 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\serializers.py first seen with mtime **********.3189974
2025-06-19 15:04:43,518 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\fallback.py first seen with mtime **********.1464849
2025-06-19 15:04:43,518 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\cookie.py first seen with mtime **********.1464849
2025-06-19 15:04:44,438 - django.channels.server - INFO - HTTP GET /copywritting/ 200 [0.00, 127.0.0.1:52074]
2025-06-19 15:04:57,420 - style_content.consumers - INFO - Received model: gpt-4o-mini
2025-06-19 15:04:57,420 - style_content.consumers - INFO - ----------------------------------------------
2025-06-19 15:04:57,420 - style_content.consumers - INFO - Received text_data: {"productIntroduction":"","model":"gpt-4o-mini","source":1,"outline":"文案大纲将在此处生成...","brand":"LV","platform":"小红书","copytheme":"","copytype":"New launch","ifuseexamples":0,"example1":"","example2":"","example3":"","copytheme2":"","useContentDescription":false,"contentDescriptionText":"","productName":"","productAudience":"","productSellingPoints":"","images":[]}
2025-06-19 15:04:57,421 - style_content.consumers - INFO - ----------------------------------------------
2025-06-19 15:04:57,421 - style_content.consumers - INFO - Received input_data: {'productIntroduction': '', 'model': 'gpt-4o-mini', 'source': 1, 'outline': '文案大纲将在此处生成...', 'brand': 'LV', 'platform': '小红书', 'copytheme': '', 'copytype': 'New launch', 'ifuseexamples': 0, 'example1': '', 'example2': '', 'example3': '', 'copytheme2': '', 'useContentDescription': False, 'contentDescriptionText': '', 'productName': '', 'productAudience': '', 'productSellingPoints': '', 'images': []}
2025-06-19 15:05:18,654 - django.db.backends - DEBUG - (0.016) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:05:18,654 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:05:18,655 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:05:18.645726' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:05:18.645726', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:05:18,657 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:05:18,659 - django.channels.server - INFO - HTTP GET / 200 [0.02, 127.0.0.1:52469]
2025-06-19 15:05:18,667 - django.channels.server - INFO - WebSocket DISCONNECT /ws/gpt/ [127.0.0.1:52077]
Connection closed with code 1001
2025-06-19 15:05:18,703 - django.channels.server - WARNING - HTTP GET /static/pic/Wilson_Blade_v9.png 404 [0.01, 127.0.0.1:52469]
2025-06-19 15:05:18,794 - django.channels.server - INFO - WebSocket HANDSHAKING /ws/gpt/ [127.0.0.1:52472]
2025-06-19 15:05:18,800 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:05:18,801 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:05:18.796001' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:05:18.796001', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:05:18,804 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:05:18,805 - django.channels.server - INFO - WebSocket CONNECT /ws/gpt/ [127.0.0.1:52472]
2025-06-19 15:05:20,896 - django.channels.server - INFO - HTTP GET /copywritting/ 200 [0.00, 127.0.0.1:52479]
2025-06-19 15:05:31,276 - style_content.consumers - INFO - Received model: gpt-4o-mini
2025-06-19 15:05:31,277 - style_content.consumers - INFO - ----------------------------------------------
2025-06-19 15:05:31,277 - style_content.consumers - INFO - Received text_data: {"productIntroduction":"","model":"gpt-4o-mini","source":1,"outline":"文案大纲将在此处生成...","brand":"LV","platform":"小红书","copytheme":"","copytype":"New launch","ifuseexamples":0,"example1":"","example2":"","example3":"","copytheme2":"","useContentDescription":false,"contentDescriptionText":"","productName":"","productAudience":"","productSellingPoints":"","images":[]}
2025-06-19 15:05:31,277 - style_content.consumers - INFO - ----------------------------------------------
2025-06-19 15:05:31,278 - style_content.consumers - INFO - Received input_data: {'productIntroduction': '', 'model': 'gpt-4o-mini', 'source': 1, 'outline': '文案大纲将在此处生成...', 'brand': 'LV', 'platform': '小红书', 'copytheme': '', 'copytype': 'New launch', 'ifuseexamples': 0, 'example1': '', 'example2': '', 'example3': '', 'copytheme2': '', 'useContentDescription': False, 'contentDescriptionText': '', 'productName': '', 'productAudience': '', 'productSellingPoints': '', 'images': []}
2025-06-19 15:11:16,074 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750316427.4607756, current mtime: 1750317075.5468335
2025-06-19 15:11:16,077 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000025FFDF84900>, True), (<function translation_file_changed at 0x0000025FFFD73600>, None)].
2025-06-19 15:11:17,182 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750317075.5468335, current mtime: 1750317077.1187334
2025-06-19 15:11:17,183 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000025FFDF84900>, True), (<function translation_file_changed at 0x0000025FFFD73600>, None)].
2025-06-19 15:11:37,000 - daphne.server - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-19 15:11:37,001 - daphne.server - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-19 15:11:37,002 - daphne.server - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-19 15:11:37,605 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime **********.321
2025-06-19 15:11:38,462 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:11:38,462 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:11:38,465 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:11:38.454165' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:11:38.454165', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:11:38,468 - django.db.backends - DEBUG - (0.015) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:11:38,470 - django.channels.server - INFO - HTTP GET / 200 [0.03, 127.0.0.1:53588]
2025-06-19 15:11:38,817 - django.channels.server - WARNING - HTTP GET /static/pic/Wilson_Blade_v9.png 404 [0.01, 127.0.0.1:53588]
2025-06-19 15:11:38,835 - django.channels.server - INFO - WebSocket HANDSHAKING /ws/gpt/ [127.0.0.1:53591]
2025-06-19 15:11:38,842 - django.db.backends - DEBUG - (0.015) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:11:38,843 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:11:38,844 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:11:38.836211' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:11:38.836211', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:11:38,846 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:11:38,847 - django.channels.server - INFO - WebSocket CONNECT /ws/gpt/ [127.0.0.1:53591]
2025-06-19 15:11:39,091 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\context_processors.py first seen with mtime **********.1434848
2025-06-19 15:11:39,102 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\fallback.py first seen with mtime **********.1464849
2025-06-19 15:11:39,105 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\session.py first seen with mtime **********.1464849
2025-06-19 15:11:39,108 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\serializers.py first seen with mtime **********.3189974
2025-06-19 15:11:39,122 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\context_processors.py first seen with mtime **********.5782743
2025-06-19 15:11:39,125 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\cookie.py first seen with mtime **********.1464849
2025-06-19 15:11:39,134 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\staticfiles\storage.py first seen with mtime **********.5785098
2025-06-19 15:11:39,147 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\template\context_processors.py first seen with mtime **********.7127118
2025-06-19 15:11:41,125 - django.channels.server - INFO - HTTP GET /copywritting/ 200 [0.01, 127.0.0.1:53588]
2025-06-19 15:13:00,518 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:13:00,519 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:13:00,521 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:13:00.509392' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:13:00.509392', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:13:00,523 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:13:01,910 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\encodings\idna.py first seen with mtime **********.0
2025-06-19 15:13:01,919 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\stringprep.py first seen with mtime **********.0
2025-06-19 15:13:01,943 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-19 15:13:11,989 - django.channels.server - INFO - HTTP POST /productinfo/ 200 [11.48, 127.0.0.1:53695]
2025-06-19 15:19:11,446 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750317077.1187334, current mtime: 1750317551.3845909
2025-06-19 15:19:11,448 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000020AE4A14900>, True), (<function translation_file_changed at 0x0000020AE77D3600>, None)].
2025-06-19 15:24:05,722 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index previous mtime: 1750152109.1317265, current mtime: 1750317844.7706766
2025-06-19 15:24:05,723 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index notified as changed. Signal results: [(<function template_changed at 0x0000020AE4A14900>, True), (<function translation_file_changed at 0x0000020AE77D3600>, None)].
2025-06-19 15:24:05,723 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750317551.3845909, current mtime: 1750317844.7706766
2025-06-19 15:24:05,723 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000020AE4A14900>, True), (<function translation_file_changed at 0x0000020AE77D3600>, None)].
2025-06-19 15:32:02,441 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750317844.7706766, current mtime: 1750318322.2200468
2025-06-19 15:32:02,441 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000020AE4A14900>, True), (<function translation_file_changed at 0x0000020AE77D3600>, None)].
2025-06-19 15:34:23,089 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750318322.2200468, current mtime: 1750318462.3130398
2025-06-19 15:34:23,091 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000020AE4A14900>, True), (<function translation_file_changed at 0x0000020AE77D3600>, None)].
2025-06-19 15:34:47,008 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750318462.3130398, current mtime: 1750318486.8555238
2025-06-19 15:34:47,010 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x0000020AE4A14900>, True), (<function translation_file_changed at 0x0000020AE77D3600>, None)].
2025-06-19 15:35:10,055 - daphne.server - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-19 15:35:10,057 - daphne.server - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-19 15:35:10,058 - daphne.server - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-19 15:35:10,149 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic_core\core_schema.py first seen with mtime **********.535289
2025-06-19 15:35:10,150 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_std_types_schema.py first seen with mtime **********.8514824
2025-06-19 15:35:10,150 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_reasoning_item_param.py first seen with mtime **********.3808885
2025-06-19 15:35:10,150 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step.py first seen with mtime **********.331889
2025-06-19 15:35:10,150 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\generics.py first seen with mtime **********.8354826
2025-06-19 15:35:10,150 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image_create_variation_params.py first seen with mtime **********.2630188
2025-06-19 15:35:10,151 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_exceptions.py first seen with mtime **********.2059197
2025-06-19 15:35:10,151 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\completions\__init__.py first seen with mtime **********.2470188
2025-06-19 15:35:10,151 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session.py first seen with mtime **********.3123617
2025-06-19 15:35:10,151 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\chat\_types.py first seen with mtime **********.232977
2025-06-19 15:35:10,151 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_update_params.py first seen with mtime **********.4018884
2025-06-19 15:35:10,152 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_text.py first seen with mtime **********.0
2025-06-19 15:35:10,152 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\migrations\loader.py first seen with mtime **********.6484532
2025-06-19 15:35:10,152 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job_wandb_integration_object.py first seen with mtime **********.3508933
2025-06-19 15:35:10,153 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step_delta_message_delta.py first seen with mtime **********.3328934
2025-06-19 15:35:10,153 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\cells.py first seen with mtime **********.848939
2025-06-19 15:35:10,153 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_function.py first seen with mtime **********.2832005
2025-06-19 15:35:10,153 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\annotated_types.py first seen with mtime **********.8584824
2025-06-19 15:35:10,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\sniffio\_impl.py first seen with mtime 1742633606.9900455
2025-06-19 15:35:10,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\responses\input_items.py first seen with mtime **********.2510195
2025-06-19 15:35:10,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_item_param.py first seen with mtime **********.374893
2025-06-19 15:35:10,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\admin\migrations\__init__.py first seen with mtime **********.4066837
2025-06-19 15:35:10,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_function_param.py first seen with mtime **********.3868883
2025-06-19 15:35:10,155 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\views\csrf.py first seen with mtime **********.7457466
2025-06-19 15:35:10,155 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_spinners.py first seen with mtime **********.8459394
2025-06-19 15:35:10,155 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\__init__.py first seen with mtime **********.8424823
2025-06-19 15:35:10,155 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\file_search_tool.py first seen with mtime **********.3548927
2025-06-19 15:35:10,155 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_refusal_delta_event.py first seen with mtime **********.3818884
2025-06-19 15:35:10,156 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\parsed_function_tool_call.py first seen with mtime **********.3478932
2025-06-19 15:35:10,156 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_web_search_call_in_progress_event.py first seen with mtime **********.385888
2025-06-19 15:35:10,156 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_model.py first seen with mtime **********.2672012
2025-06-19 15:35:10,157 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\filecontrol\migrations\0001_initial.py first seen with mtime 1733578442.0
2025-06-19 15:35:10,158 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_events.py first seen with mtime **********.2220497
2025-06-19 15:35:10,158 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\main.py first seen with mtime **********.8644824
2025-06-19 15:35:10,159 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\exceptions.py first seen with mtime **********.1055863
2025-06-19 15:35:10,160 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_text_delta_event.py first seen with mtime **********.2772007
2025-06-19 15:35:10,160 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_input_audio_transcription_failed_event.py first seen with mtime **********.2942014
2025-06-19 15:35:10,160 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_delta.py first seen with mtime **********.3224494
2025-06-19 15:35:10,161 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\compound_filter.py first seen with mtime **********.3908885
2025-06-19 15:35:10,161 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_synchronization.py first seen with mtime **********.5962598
2025-06-19 15:35:10,161 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_ratio.py first seen with mtime **********.844939
2025-06-19 15:35:10,162 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_status.py first seen with mtime **********.3254492
2025-06-19 15:35:10,162 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py first seen with mtime 1750316675.2093546
2025-06-19 15:35:10,162 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\migrations\0001_initial.py first seen with mtime **********.479591
2025-06-19 15:35:10,163 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_import_utils.py first seen with mtime **********.8484826
2025-06-19 15:35:10,163 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch_request_counts.py first seen with mtime **********.2580192
2025-06-19 15:35:10,163 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url.py first seen with mtime **********.3184493
2025-06-19 15:35:10,163 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0008_alter_user_username_max_length.py first seen with mtime **********.7043734
2025-06-19 15:35:10,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call_output_item.py first seen with mtime **********.3618937
2025-06-19 15:35:10,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call.py first seen with mtime **********.3618937
2025-06-19 15:35:10,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_resources.py first seen with mtime **********.6791902
2025-06-19 15:35:10,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_response.py first seen with mtime **********.2089188
2025-06-19 15:35:10,165 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\connection_pool.py first seen with mtime **********.5983443
2025-06-19 15:35:10,166 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\middleware.py first seen with mtime **********.1434848
2025-06-19 15:35:10,166 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image.py first seen with mtime **********.2630188
2025-06-19 15:35:10,167 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0001_initial.py first seen with mtime **********.0
2025-06-19 15:35:10,169 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session_update_param.py first seen with mtime **********.3134074
2025-06-19 15:35:10,169 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\function_tool_call.py first seen with mtime **********.330889
2025-06-19 15:35:10,169 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\views.py first seen with mtime **********.5822737
2025-06-19 15:35:10,170 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\realtime\realtime.py first seen with mtime **********.2430186
2025-06-19 15:35:10,171 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_constants.py first seen with mtime **********.2059197
2025-06-19 15:35:10,171 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\decorator.py first seen with mtime **********.8614824
2025-06-19 15:35:10,172 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\chat\completions.py first seen with mtime **********.2420187
2025-06-19 15:35:10,173 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_audio_done_event.py first seen with mtime **********.3044255
2025-06-19 15:35:10,173 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\fields.py first seen with mtime **********.8344827
2025-06-19 15:35:10,173 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_update_params.py first seen with mtime **********.2852006
2025-06-19 15:35:10,174 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\tool_call.py first seen with mtime **********.3338926
2025-06-19 15:35:10,174 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\completion_choice.py first seen with mtime **********.2580192
2025-06-19 15:35:10,174 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image_edit_params.py first seen with mtime **********.264019
2025-06-19 15:35:10,175 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\response_format_json_schema.py first seen with mtime **********.3978882
2025-06-19 15:35:10,175 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\__init__.py first seen with mtime **********.3898883
2025-06-19 15:35:10,175 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_pick.py first seen with mtime **********.844939
2025-06-19 15:35:10,175 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_message_param.py first seen with mtime **********.3408928
2025-06-19 15:35:10,176 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread.py first seen with mtime **********.2872007
2025-06-19 15:35:10,176 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_testing.py first seen with mtime **********.68119
2025-06-19 15:35:10,176 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session.py first seen with mtime **********.309369
2025-06-19 15:35:10,176 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\threads.py first seen with mtime **********.2450187
2025-06-19 15:35:10,176 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_types.py first seen with mtime **********.124564
2025-06-19 15:35:10,177 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\tools\update_usage.py first seen with mtime 1733578445.0
2025-06-19 15:35:10,177 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\__init__.py first seen with mtime **********.2028618
2025-06-19 15:35:10,177 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\file_path_annotation.py first seen with mtime **********.3164494
2025-06-19 15:35:10,177 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\migrations\recorder.py first seen with mtime **********.6494534
2025-06-19 15:35:10,177 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\__init__.py first seen with mtime **********.3144498
2025-06-19 15:35:10,178 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_deleted.py first seen with mtime **********.338893
2025-06-19 15:35:10,178 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_commit_event.py first seen with mtime **********.298427
2025-06-19 15:35:10,178 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_update_params.py first seen with mtime **********.3264492
2025-06-19 15:35:10,178 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_module_client.py first seen with mtime **********.207919
2025-06-19 15:35:10,178 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\regex\__init__.py first seen with mtime **********.893174
2025-06-19 15:35:10,179 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_tool_param.py first seen with mtime **********.3458936
2025-06-19 15:35:10,179 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool.py first seen with mtime **********.2822006
2025-06-19 15:35:10,179 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\migrations\0002_delete_usage.py first seen with mtime 1733578445.0
2025-06-19 15:35:10,179 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_tool_call_param.py first seen with mtime **********.370893
2025-06-19 15:35:10,180 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\warnings.py first seen with mtime **********.8424823
2025-06-19 15:35:10,180 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\certifi\__init__.py first seen with mtime **********.4121935
2025-06-19 15:35:10,180 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_code_delta_event.py first seen with mtime **********.3588927
2025-06-19 15:35:10,180 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\function_tool_call_delta.py first seen with mtime **********.330889
2025-06-19 15:35:10,180 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\jobs\checkpoint_list_params.py first seen with mtime **********.3528886
2025-06-19 15:35:10,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\step_retrieve_params.py first seen with mtime **********.3338926
2025-06-19 15:35:10,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\filesize.py first seen with mtime **********.853939
2025-06-19 15:35:10,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\annotated_types\__init__.py first seen with mtime **********.4265754
2025-06-19 15:35:10,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_create_response.py first seen with mtime **********.310366
2025-06-19 15:35:10,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\util.py first seen with mtime **********.8118074
2025-06-19 15:35:10,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\mock.py first seen with mtime **********.6023443
2025-06-19 15:35:10,182 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\streams\stapled.py first seen with mtime **********.6821902
2025-06-19 15:35:10,182 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\regex\_regex_core.py first seen with mtime **********.8971746
2025-06-19 15:35:10,182 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\web_search_tool.py first seen with mtime **********.3888881
2025-06-19 15:35:10,182 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\__init__.py first seen with mtime **********.3998883
2025-06-19 15:35:10,182 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0005_usage_idx_userid_usetime.py first seen with mtime **********.0
2025-06-19 15:35:10,182 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_serializers.py first seen with mtime **********.8504827
2025-06-19 15:35:10,183 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\readers.py first seen with mtime **********.0
2025-06-19 15:35:10,183 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\computer_tool_param.py first seen with mtime **********.3548927
2025-06-19 15:35:10,184 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\views\defaults.py first seen with mtime **********.7457466
2025-06-19 15:35:10,184 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:35:10,184 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_forward_ref.py first seen with mtime **********.8464828
2025-06-19 15:35:10,184 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_trace.py first seen with mtime **********.5962598
2025-06-19 15:35:10,184 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:35:10,184 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_create_params.py first seen with mtime **********.2712011
2025-06-19 15:35:10,185 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_input_audio_transcription_completed_event.py first seen with mtime **********.293202
2025-06-19 15:35:10,185 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\comparison_filter.py first seen with mtime **********.395888
2025-06-19 15:35:10,185 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_types_param.py first seen with mtime **********.3878887
2025-06-19 15:35:10,186 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\upload_complete_params.py first seen with mtime **********.2702012
2025-06-19 15:35:10,186 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_token_logprob.py first seen with mtime **********.344893
2025-06-19 15:35:10,186 - django.db.backends - DEBUG - (0.016) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:35:10.172066' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:35:10.172066', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:35:10,186 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\connection.py first seen with mtime **********.6033444
2025-06-19 15:35:10,188 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_call_completed_event.py first seen with mtime **********.365893
2025-06-19 15:35:10,189 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription.py first seen with mtime **********.2752008
2025-06-19 15:35:10,189 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text.py first seen with mtime **********.3264492
2025-06-19 15:35:10,189 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_deleted.py first seen with mtime **********.2712011
2025-06-19 15:35:10,189 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch.py first seen with mtime **********.2560194
2025-06-19 15:35:10,190 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_emoji_replace.py first seen with mtime **********.841953
2025-06-19 15:35:10,190 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_tool_call.py first seen with mtime **********.369893
2025-06-19 15:35:10,190 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_create_params.py first seen with mtime **********.2660847
2025-06-19 15:35:10,190 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:35:10,191 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\responses\__init__.py first seen with mtime **********.2510195
2025-06-19 15:35:10,191 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_message_content_list.py first seen with mtime **********.374893
2025-06-19 15:35:10,192 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\translation_verbose.py first seen with mtime **********.2792006
2025-06-19 15:35:10,193 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0004_rename_token_num_usage_output_token_and_more.py first seen with mtime **********.0
2025-06-19 15:35:10,192 - django.channels.server - INFO - HTTP GET / 200 [0.03, 127.0.0.1:56545]
2025-06-19 15:35:10,193 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_decoders.py first seen with mtime **********.122479
2025-06-19 15:35:10,193 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\streams\__init__.py first seen with mtime **********.68119
2025-06-19 15:35:10,194 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response.py first seen with mtime **********.3578937
2025-06-19 15:35:10,194 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_utils.py first seen with mtime **********.8524857
2025-06-19 15:35:10,194 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_error_event.py first seen with mtime **********.364893
2025-06-19 15:35:10,194 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\jiter\jiter.cp311-win_amd64.pyd first seen with mtime **********.065094
2025-06-19 15:35:10,195 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_transform.py first seen with mtime **********.2149189
2025-06-19 15:35:10,195 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_audio_transcript_done_event.py first seen with mtime **********.3044255
2025-06-19 15:35:10,195 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_docs_extraction.py first seen with mtime **********.8454869
2025-06-19 15:35:10,196 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\middleware.py first seen with mtime **********.317997
2025-06-19 15:35:10,196 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_core_utils.py first seen with mtime **********.8434825
2025-06-19 15:35:10,196 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_message.py first seen with mtime **********.3788888
2025-06-19 15:35:10,197 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\file_search_tool_param.py first seen with mtime **********.286201
2025-06-19 15:35:10,197 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_with_reference.py first seen with mtime **********.296202
2025-06-19 15:35:10,198 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_deleted.py first seen with mtime **********.3214495
2025-06-19 15:35:10,198 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\job_create_params.py first seen with mtime **********.3508933
2025-06-19 15:35:10,198 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\vector_store_file_deleted.py first seen with mtime **********.4028885
2025-06-19 15:35:10,199 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_old_api.py first seen with mtime **********.2269764
2025-06-19 15:35:10,199 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_content_part_added_event.py first seen with mtime **********.362893
2025-06-19 15:35:10,199 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_truncated_event.py first seen with mtime **********.296202
2025-06-19 15:35:10,199 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_decorators.py first seen with mtime **********.8444824
2025-06-19 15:35:10,200 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_param.py first seen with mtime **********.3204496
2025-06-19 15:35:10,200 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\__init__.py first seen with mtime **********.5942636
2025-06-19 15:35:10,200 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_config.py first seen with mtime **********.8434825
2025-06-19 15:35:10,200 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\model_deleted.py first seen with mtime **********.265019
2025-06-19 15:35:10,201 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_cancel_event_param.py first seen with mtime **********.3054254
2025-06-19 15:35:10,201 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store.py first seen with mtime **********.2712011
2025-06-19 15:35:10,201 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\interfaces.py first seen with mtime **********.5993447
2025-06-19 15:35:10,201 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\__init__.py first seen with mtime **********.2742007
2025-06-19 15:35:10,202 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_assistant_message_param.py first seen with mtime **********.3358927
2025-06-19 15:35:10,202 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\db.py first seen with mtime **********.1055863
2025-06-19 15:35:10,202 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_clear_event.py first seen with mtime **********.298427
2025-06-19 15:35:10,203 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\computer_tool.py first seen with mtime **********.353893
2025-06-19 15:35:10,203 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\types.py first seen with mtime **********.8404825
2025-06-19 15:35:10,203 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_list_params.py first seen with mtime **********.2722006
2025-06-19 15:35:10,204 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\admin\migrations\0003_logentry_add_action_flag_choices.py first seen with mtime **********.4056838
2025-06-19 15:35:10,204 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\_adapters.py first seen with mtime **********.0
2025-06-19 15:35:10,205 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\pandas_proxy.py first seen with mtime **********.2119188
2025-06-19 15:35:10,205 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_completed_event.py first seen with mtime **********.3598928
2025-06-19 15:35:10,205 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio_response_format.py first seen with mtime **********.2560194
2025-06-19 15:35:10,206 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_tempfile.py first seen with mtime **********.6771903
2025-06-19 15:35:10,206 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\pager.py first seen with mtime **********.8579438
2025-06-19 15:35:10,206 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\jobs\checkpoints.py first seen with mtime **********.2500196
2025-06-19 15:35:10,206 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\middleware.py first seen with mtime **********.1065862
2025-06-19 15:35:10,207 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_cell_widths.py first seen with mtime **********.8409388
2025-06-19 15:35:10,207 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_content_block.py first seen with mtime **********.3194492
2025-06-19 15:35:10,207 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\registry.py first seen with mtime **********.982582
2025-06-19 15:35:10,208 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_modality.py first seen with mtime **********.341893
2025-06-19 15:35:10,208 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_prediction_content_param.py first seen with mtime **********.3428926
2025-06-19 15:35:10,209 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\completions\messages.py first seen with mtime **********.248019
2025-06-19 15:35:10,209 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job.py first seen with mtime **********.3498929
2025-06-19 15:35:10,209 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_role.py first seen with mtime **********.3428926
2025-06-19 15:35:10,210 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\annotated_handlers.py first seen with mtime **********.8314824
2025-06-19 15:35:10,210 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\zstandard\backend_c.cp311-win_amd64.pyd first seen with mtime 1742633606.8127646
2025-06-19 15:35:10,210 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_typing_extra.py first seen with mtime **********.8514824
2025-06-19 15:35:10,211 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\to_thread.py first seen with mtime **********.6711361
2025-06-19 15:35:10,211 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\code_interpreter_tool_call_delta.py first seen with mtime **********.3298812
2025-06-19 15:35:10,211 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\panel.py first seen with mtime **********.8589437
2025-06-19 15:35:10,211 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_committed_event.py first seen with mtime **********.2994251
2025-06-19 15:35:10,212 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_models.py first seen with mtime **********.2069192
2025-06-19 15:35:10,212 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_tasks.py first seen with mtime **********.6771903
2025-06-19 15:35:10,212 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_tool_call_output_item.py first seen with mtime **********.369893
2025-06-19 15:35:10,213 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_list_params.py first seen with mtime **********.3254492
2025-06-19 15:35:10,213 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\__init__.py first seen with mtime **********.2129188
2025-06-19 15:35:10,213 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\contenttypes\migrations\__init__.py first seen with mtime **********.8052022
2025-06-19 15:35:10,213 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_function.py first seen with mtime **********.3868883
2025-06-19 15:35:10,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_incomplete_event.py first seen with mtime **********.3718927
2025-06-19 15:35:10,214 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\__init__.py first seen with mtime **********.0
2025-06-19 15:35:10,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\assistants.py first seen with mtime **********.2400205
2025-06-19 15:35:10,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\http_proxy.py first seen with mtime **********.6043444
2025-06-19 15:35:10,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_delta.py first seen with mtime **********.3194492
2025-06-19 15:35:10,215 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_created_event.py first seen with mtime **********.3074307
2025-06-19 15:35:10,215 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_types.py first seen with mtime **********.3878887
2025-06-19 15:35:10,215 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_clear_event_param.py first seen with mtime **********.298427
2025-06-19 15:35:10,216 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_tool_call_param.py first seen with mtime **********.3668928
2025-06-19 15:35:10,216 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0006_require_contenttypes_0002.py first seen with mtime **********.7033734
2025-06-19 15:35:10,217 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text_delta.py first seen with mtime **********.3274493
2025-06-19 15:35:10,217 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_connect_params.py first seen with mtime **********.3014345
2025-06-19 15:35:10,217 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_refusal_param.py first seen with mtime **********.338893
2025-06-19 15:35:10,218 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\progress_bar.py first seen with mtime **********.8599439
2025-06-19 15:35:10,218 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\__init__.py first seen with mtime **********.2470188
2025-06-19 15:35:10,218 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_reasoning_effort.py first seen with mtime **********.3428926
2025-06-19 15:35:10,218 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\region.py first seen with mtime **********.8619478
2025-06-19 15:35:10,219 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_item.py first seen with mtime **********.377889
2025-06-19 15:35:10,219 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\from_thread.py first seen with mtime **********.669136
2025-06-19 15:35:10,219 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\runs\runs.py first seen with mtime **********.246019
2025-06-19 15:35:10,219 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_text_param.py first seen with mtime **********.3808885
2025-06-19 15:35:10,219 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_delta_event.py first seen with mtime **********.3224494
2025-06-19 15:35:10,220 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\token.py first seen with mtime **********.8108082
2025-06-19 15:35:10,220 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\jobs\__init__.py first seen with mtime **********.3528886
2025-06-19 15:35:10,220 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\upload.py first seen with mtime **********.2702012
2025-06-19 15:35:10,220 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\asgi.py first seen with mtime **********.1265635
2025-06-19 15:35:10,221 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\response_format_text.py first seen with mtime **********.3938882
2025-06-19 15:35:10,221 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\function_tool.py first seen with mtime **********.286201
2025-06-19 15:35:10,221 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\uploads\part_create_params.py first seen with mtime **********.3998883
2025-06-19 15:35:10,221 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_content_delta.py first seen with mtime **********.3214495
2025-06-19 15:35:10,222 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_response_usage.py first seen with mtime **********.3024244
2025-06-19 15:35:10,222 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0011_update_proxy_permissions.py first seen with mtime **********.7043734
2025-06-19 15:35:10,222 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\jupyter.py first seen with mtime **********.854947
2025-06-19 15:35:10,222 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0002_alter_permission_name_max_length.py first seen with mtime **********.701161
2025-06-19 15:35:10,223 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\completions.py first seen with mtime **********.2349768
2025-06-19 15:35:10,223 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\tool_calls_step_details.py first seen with mtime **********.334893
2025-06-19 15:35:10,223 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_content_block_param.py first seen with mtime **********.3194492
2025-06-19 15:35:10,223 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_resources.py first seen with mtime **********.6751904
2025-06-19 15:35:10,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_call_arguments_done_event.py first seen with mtime **********.368893
2025-06-19 15:35:10,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\chat_model.py first seen with mtime **********.3948882
2025-06-19 15:35:10,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_reflection.py first seen with mtime **********.2139187
2025-06-19 15:35:10,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\error_wrappers.py first seen with mtime **********.8624823
2025-06-19 15:35:10,225 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_message_param.py first seen with mtime **********.3788888
2025-06-19 15:35:10,225 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\annotation_delta.py first seen with mtime **********.3154497
2025-06-19 15:35:10,225 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_object.py first seen with mtime **********.2630188
2025-06-19 15:35:10,226 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_urlparse.py first seen with mtime **********.124564
2025-06-19 15:35:10,226 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_functools.py first seen with mtime **********.0
2025-06-19 15:35:10,226 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_log_render.py first seen with mtime **********.843939
2025-06-19 15:35:10,227 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_known_annotated_metadata.py first seen with mtime **********.8484826
2025-06-19 15:35:10,227 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_batch_create_params.py first seen with mtime **********.4008884
2025-06-19 15:35:10,228 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\box.py first seen with mtime **********.848939
2025-06-19 15:35:10,228 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\networks.py first seen with mtime **********.8654823
2025-06-19 15:35:10,229 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session_updated_event.py first seen with mtime **********.3134074
2025-06-19 15:35:10,229 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text_content_block_param.py first seen with mtime **********.3274493
2025-06-19 15:35:10,229 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_image_param.py first seen with mtime **********.3378928
2025-06-19 15:35:10,230 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_create_params.py first seen with mtime **********.310366
2025-06-19 15:35:10,230 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_text_done_event.py first seen with mtime **********.309369
2025-06-19 15:35:10,230 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_web_search_param.py first seen with mtime **********.370893
2025-06-19 15:35:10,230 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio_model.py first seen with mtime **********.2550194
2025-06-19 15:35:10,230 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\gpt_CWSC\asgi.py first seen with mtime **********.0
2025-06-19 15:35:10,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_deleted.py first seen with mtime **********.262019
2025-06-19 15:35:10,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_store_message.py first seen with mtime **********.3428926
2025-06-19 15:35:10,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\lexers\_mapping.py first seen with mtime **********.8238082
2025-06-19 15:35:10,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text_delta_block.py first seen with mtime **********.3274493
2025-06-19 15:35:10,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\translations.py first seen with mtime **********.2390192
2025-06-19 15:35:10,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item.py first seen with mtime **********.2902007
2025-06-19 15:35:10,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\ansi.py first seen with mtime **********.847939
2025-06-19 15:35:10,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\runs\steps.py first seen with mtime **********.246019
2025-06-19 15:35:10,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_input_audio_param.py first seen with mtime **********.3378928
2025-06-19 15:35:10,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\core.py first seen with mtime **********.9805818
2025-06-19 15:35:10,233 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_error.py first seen with mtime **********.364893
2025-06-19 15:35:10,233 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_utils.py first seen with mtime **********.5962598
2025-06-19 15:35:10,234 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\core\asgi.py first seen with mtime **********.5846596
2025-06-19 15:35:10,234 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_text_input_param.py first seen with mtime **********.2682009
2025-06-19 15:35:10,234 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step_delta.py first seen with mtime **********.3328934
2025-06-19 15:35:10,234 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\__init__.py first seen with mtime **********.2119188
2025-06-19 15:35:10,235 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\function_definition.py first seen with mtime **********.3918886
2025-06-19 15:35:10,235 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\filecontrol\migrations\0002_alter_fileinfo_upload_user.py first seen with mtime 1733578442.0
2025-06-19 15:35:10,235 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\anyio.py first seen with mtime **********.6003442
2025-06-19 15:35:10,235 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_append_event_param.py first seen with mtime **********.2973366
2025-06-19 15:35:10,236 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\live_render.py first seen with mtime **********.8559444
2025-06-19 15:35:10,236 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\completion_usage.py first seen with mtime **********.259019
2025-06-19 15:35:10,236 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\job_list_params.py first seen with mtime **********.3518999
2025-06-19 15:35:10,236 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\fractions.py first seen with mtime **********.0
2025-06-19 15:35:10,237 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\exceptions.py first seen with mtime **********.317997
2025-06-19 15:35:10,237 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_extension.py first seen with mtime **********.842939
2025-06-19 15:35:10,237 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_config_param.py first seen with mtime **********.3838882
2025-06-19 15:35:10,237 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_cleared_event.py first seen with mtime **********.298427
2025-06-19 15:35:10,238 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_response_format_option.py first seen with mtime **********.2812006
2025-06-19 15:35:10,238 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completions\message_list_params.py first seen with mtime **********.3488927
2025-06-19 15:35:10,238 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_output_item_added_event.py first seen with mtime **********.308454
2025-06-19 15:35:10,239 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\responses\responses.py first seen with mtime **********.2510195
2025-06-19 15:35:10,239 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_options.py first seen with mtime **********.3868883
2025-06-19 15:35:10,239 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\live.py first seen with mtime **********.8559444
2025-06-19 15:35:10,240 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\screen.py first seen with mtime **********.8639438
2025-06-19 15:35:10,240 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\constrain.py first seen with mtime **********.8509388
2025-06-19 15:35:10,240 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\base.py first seen with mtime **********.1265635
2025-06-19 15:35:10,240 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\tools\count_token.py first seen with mtime 1733578445.0
2025-06-19 15:35:10,241 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_delta.py first seen with mtime **********.317449
2025-06-19 15:35:10,241 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_submit_tool_outputs_params.py first seen with mtime **********.3254492
2025-06-19 15:35:10,242 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\control.py first seen with mtime **********.851939
2025-06-19 15:35:10,242 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_search_params.py first seen with mtime **********.2722006
2025-06-19 15:35:10,242 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_response_format_option_param.py first seen with mtime **********.2812006
2025-06-19 15:35:10,243 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_client_event.py first seen with mtime **********.3014345
2025-06-19 15:35:10,243 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_mock_val_ser.py first seen with mtime **********.8494825
2025-06-19 15:35:10,244 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\__init__.py first seen with mtime **********.8399444
2025-06-19 15:35:10,244 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\uploads\__init__.py first seen with mtime **********.3988883
2025-06-19 15:35:10,245 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\__init__.py first seen with mtime **********.67819
2025-06-19 15:35:10,245 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\highlighter.py first seen with mtime **********.853939
2025-06-19 15:35:10,246 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_compat.py first seen with mtime **********.2049081
2025-06-19 15:35:10,247 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\responses_model.py first seen with mtime **********.3948882
2025-06-19 15:35:10,247 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\spinner.py first seen with mtime **********.8639438
2025-06-19 15:35:10,247 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\numpy_proxy.py first seen with mtime **********.2119188
2025-06-19 15:35:10,247 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\json.py first seen with mtime **********.8634825
2025-06-19 15:35:10,248 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\readers.py first seen with mtime **********.0
2025-06-19 15:35:10,248 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_stream_event.py first seen with mtime **********.2762005
2025-06-19 15:35:10,248 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\plugin.py first seen with mtime **********.808808
2025-06-19 15:35:10,249 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_update_params.py first seen with mtime **********.2732008
2025-06-19 15:35:10,249 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_emoji_codes.py first seen with mtime **********.841953
2025-06-19 15:35:10,249 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_list_params.py first seen with mtime **********.4018884
2025-06-19 15:35:10,249 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_list_params.py first seen with mtime **********.262019
2025-06-19 15:35:10,250 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_image_url_input_param.py first seen with mtime **********.2672012
2025-06-19 15:35:10,250 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_utils.py first seen with mtime **********.2159152
2025-06-19 15:35:10,250 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\filecontrol\migrations\__init__.py first seen with mtime 1733578442.0
2025-06-19 15:35:10,250 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\other_file_chunking_strategy_object.py first seen with mtime **********.2682009
2025-06-19 15:35:10,251 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\required_action_function_tool_call.py first seen with mtime **********.3244493
2025-06-19 15:35:10,251 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\admin\migrations\0001_initial.py first seen with mtime **********.4056838
2025-06-19 15:35:10,251 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_param.py first seen with mtime **********.3378928
2025-06-19 15:35:10,251 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\style.py first seen with mtime **********.8098085
2025-06-19 15:35:10,252 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0005_alter_user_last_login_null.py first seen with mtime **********.7021997
2025-06-19 15:35:10,252 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\text.py first seen with mtime **********.8679438
2025-06-19 15:35:10,252 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_tools.py first seen with mtime **********.2269764
2025-06-19 15:35:10,252 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\fine_tuning.py first seen with mtime **********.24902
2025-06-19 15:35:10,252 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\uploads\uploads.py first seen with mtime **********.2530196
2025-06-19 15:35:10,253 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_create_event.py first seen with mtime **********.291201
2025-06-19 15:35:10,253 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\code_interpreter_logs.py first seen with mtime **********.3284492
2025-06-19 15:35:10,253 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\vector_store_file.py first seen with mtime **********.4028885
2025-06-19 15:35:10,253 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\styles\_mapping.py first seen with mtime **********.9168081
2025-06-19 15:35:10,254 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\__init__.py first seen with mtime **********.2259765
2025-06-19 15:35:10,254 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\static_file_chunking_strategy.py first seen with mtime **********.2682009
2025-06-19 15:35:10,254 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\file_search_tool_call.py first seen with mtime **********.330889
2025-06-19 15:35:10,254 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\metadata.py first seen with mtime **********.3968883
2025-06-19 15:35:10,255 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_create_params.py first seen with mtime **********.4018884
2025-06-19 15:35:10,255 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_create_params.py first seen with mtime **********.262019
2025-06-19 15:35:10,255 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\connection_pool.py first seen with mtime **********.6033444
2025-06-19 15:35:10,255 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_include.py first seen with mtime **********.2762005
2025-06-19 15:35:10,255 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\messages.py first seen with mtime **********.2440188
2025-06-19 15:35:10,256 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_client_event_param.py first seen with mtime **********.3014345
2025-06-19 15:35:10,256 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_loop.py first seen with mtime **********.843939
2025-06-19 15:35:10,257 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\reasoning_effort.py first seen with mtime **********.3978882
2025-06-19 15:35:10,257 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_audio_transcript_delta_event.py first seen with mtime **********.3044255
2025-06-19 15:35:10,257 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\generic\__init__.py first seen with mtime **********.108586
2025-06-19 15:35:10,258 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\vector_stores\file_batches.py first seen with mtime **********.2540193
2025-06-19 15:35:10,259 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_includable.py first seen with mtime **********.3718927
2025-06-19 15:35:10,259 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\__init__.py first seen with mtime **********.0
2025-06-19 15:35:10,260 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\modeline.py first seen with mtime **********.808808
2025-06-19 15:35:10,260 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\jiter\__init__.py first seen with mtime **********.0609927
2025-06-19 15:35:10,260 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\plugin\_loader.py first seen with mtime **********.8574824
2025-06-19 15:35:10,260 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\style.py first seen with mtime **********.864944
2025-06-19 15:35:10,261 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\palette.py first seen with mtime **********.8589437
2025-06-19 15:35:10,261 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_deleted_event.py first seen with mtime **********.2922018
2025-06-19 15:35:10,261 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\create_embedding_response.py first seen with mtime **********.2600193
2025-06-19 15:35:10,261 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\pagination.py first seen with mtime **********.210919
2025-06-19 15:35:10,262 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\azure.py first seen with mtime **********.2279763
2025-06-19 15:35:10,262 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\_assistants.py first seen with mtime **********.2299788
2025-06-19 15:35:10,262 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\images_response.py first seen with mtime **********.265019
2025-06-19 15:35:10,263 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_delta_event.py first seen with mtime **********.3848884
2025-06-19 15:35:10,263 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_exceptions.py first seen with mtime **********.5952597
2025-06-19 15:35:10,263 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_response_status.py first seen with mtime **********.3024244
2025-06-19 15:35:10,264 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\responses\_types.py first seen with mtime **********.2339766
2025-06-19 15:35:10,264 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_client.py first seen with mtime **********.2049081
2025-06-19 15:35:10,265 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\__init__.py first seen with mtime **********.24902
2025-06-19 15:35:10,265 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_item_done_event.py first seen with mtime **********.3788888
2025-06-19 15:35:10,265 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_text_done_event.py first seen with mtime **********.2772007
2025-06-19 15:35:10,266 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_synchronization.py first seen with mtime **********.6761901
2025-06-19 15:35:10,266 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_text_param.py first seen with mtime **********.3768888
2025-06-19 15:35:10,266 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\embedding_create_params.py first seen with mtime **********.2600193
2025-06-19 15:35:10,266 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\_itertools.py first seen with mtime **********.0
2025-06-19 15:35:10,267 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_named_tool_choice_param.py first seen with mtime **********.341893
2025-06-19 15:35:10,267 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\completions\completions.py first seen with mtime **********.248019
2025-06-19 15:35:10,267 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\websocket_connection_options.py first seen with mtime **********.2732008
2025-06-19 15:35:10,267 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_batch_list_files_params.py first seen with mtime **********.4008884
2025-06-19 15:35:10,268 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_text_param.py first seen with mtime **********.338893
2025-06-19 15:35:10,268 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\table.py first seen with mtime **********.8669436
2025-06-19 15:35:10,269 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_format_text_json_schema_config.py first seen with mtime **********.3678927
2025-06-19 15:35:10,269 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_text.py first seen with mtime **********.3798888
2025-06-19 15:35:10,269 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_exceptions.py first seen with mtime **********.122479
2025-06-19 15:35:10,269 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_delete_event.py first seen with mtime **********.2922018
2025-06-19 15:35:10,270 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_deleted.py first seen with mtime **********.2802007
2025-06-19 15:35:10,270 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\chat\__init__.py first seen with mtime **********.2309766
2025-06-19 15:35:10,270 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\streams\memory.py first seen with mtime **********.6821902
2025-06-19 15:35:10,271 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\env_settings.py first seen with mtime **********.8614824
2025-06-19 15:35:10,271 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_typedattr.py first seen with mtime **********.67819
2025-06-19 15:35:10,271 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_subprocesses.py first seen with mtime **********.6801903
2025-06-19 15:35:10,271 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_validators.py first seen with mtime **********.8524857
2025-06-19 15:35:10,272 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_parsing\_completions.py first seen with mtime **********.2289767
2025-06-19 15:35:10,272 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\reasoning.py first seen with mtime **********.3968883
2025-06-19 15:35:10,272 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\code_interpreter_output_image.py first seen with mtime **********.3294454
2025-06-19 15:35:10,273 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_speech_stopped_event.py first seen with mtime **********.300424
2025-06-19 15:35:10,273 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\__init__.py first seen with mtime **********.829813
2025-06-19 15:35:10,273 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_message_tool_call.py first seen with mtime **********.3408928
2025-06-19 15:35:10,273 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_tool_choice_option_param.py first seen with mtime **********.344893
2025-06-19 15:35:10,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\error_event.py first seen with mtime **********.2973366
2025-06-19 15:35:10,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\speech_model.py first seen with mtime **********.2742007
2025-06-19 15:35:10,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\__init__.py first seen with mtime **********.7053769
2025-06-19 15:35:10,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\fields.py first seen with mtime **********.8634825
2025-06-19 15:35:10,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\static_file_chunking_strategy_param.py first seen with mtime **********.2692013
2025-06-19 15:35:10,275 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_call_searching_event.py first seen with mtime **********.3668928
2025-06-19 15:35:10,275 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_content_part_done_event.py first seen with mtime **********.3638935
2025-06-19 15:35:10,275 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_create_event_param.py first seen with mtime **********.291201
2025-06-19 15:35:10,275 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_create_params.py first seen with mtime **********.3214495
2025-06-19 15:35:10,276 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_chunk.py first seen with mtime **********.336893
2025-06-19 15:35:10,276 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_streams.py first seen with mtime **********.2149189
2025-06-19 15:35:10,276 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation.py first seen with mtime **********.266019
2025-06-19 15:35:10,276 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_repr.py first seen with mtime **********.8504827
2025-06-19 15:35:10,277 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\migrations\graph.py first seen with mtime **********.6474533
2025-06-19 15:35:10,277 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message.py first seen with mtime **********.3204496
2025-06-19 15:35:10,277 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_adapters.py first seen with mtime **********.0
2025-06-19 15:35:10,277 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\function_tool_param.py first seen with mtime **********.2872007
2025-06-19 15:35:10,277 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_text.py first seen with mtime **********.3768888
2025-06-19 15:35:10,278 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call_param.py first seen with mtime **********.362893
2025-06-19 15:35:10,278 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_stream_event.py first seen with mtime **********.2822006
2025-06-19 15:35:10,279 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_multipart.py first seen with mtime **********.124564
2025-06-19 15:35:10,279 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_call_arguments_delta_event.py first seen with mtime **********.368893
2025-06-19 15:35:10,280 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_sockets.py first seen with mtime **********.6751904
2025-06-19 15:35:10,280 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\tool_call_delta.py first seen with mtime **********.334893
2025-06-19 15:35:10,280 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\response_format_json_schema.py first seen with mtime **********.3938882
2025-06-19 15:35:10,281 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_done_event.py first seen with mtime **********.3074307
2025-06-19 15:35:10,281 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\base.py first seen with mtime **********.3199975
2025-06-19 15:35:10,281 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_audio_transcript_done_event.py first seen with mtime **********.3588927
2025-06-19 15:35:10,281 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_sync.py first seen with mtime **********.2149189
2025-06-19 15:35:10,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\base.py first seen with mtime **********.6013443
2025-06-19 15:35:10,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\chat\chat.py first seen with mtime **********.2420187
2025-06-19 15:35:10,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\vector_store_file_batch.py first seen with mtime **********.4028885
2025-06-19 15:35:10,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\chat\_events.py first seen with mtime **********.2319767
2025-06-19 15:35:10,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\sniffio\_version.py first seen with mtime 1742633606.9900455
2025-06-19 15:35:10,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\color_triplet.py first seen with mtime **********.8499389
2025-06-19 15:35:10,283 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_with_reference_param.py first seen with mtime **********.296202
2025-06-19 15:35:10,283 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image_generate_params.py first seen with mtime **********.264019
2025-06-19 15:35:10,283 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\error_object.py first seen with mtime **********.3918886
2025-06-19 15:35:10,283 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\base\migrations\__init__.py first seen with mtime 1733578441.0
2025-06-19 15:35:10,283 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\images.py first seen with mtime **********.236011
2025-06-19 15:35:10,284 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_content_block_param.py first seen with mtime **********.317449
2025-06-19 15:35:10,284 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\progress.py first seen with mtime **********.8599439
2025-06-19 15:35:10,284 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\functional_validators.py first seen with mtime **********.8354826
2025-06-19 15:35:10,285 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_meta.py first seen with mtime **********.0
2025-06-19 15:35:10,285 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\model.py first seen with mtime **********.265019
2025-06-19 15:35:10,285 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\terminal_theme.py first seen with mtime **********.8669436
2025-06-19 15:35:10,285 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\files.py first seen with mtime **********.236011
2025-06-19 15:35:10,285 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\interfaces.py first seen with mtime **********.6053448
2025-06-19 15:35:10,285 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_core_metadata.py first seen with mtime **********.8434825
2025-06-19 15:35:10,286 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\__init__.py first seen with mtime **********.0
2025-06-19 15:35:10,286 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_search_response.py first seen with mtime **********.2732008
2025-06-19 15:35:10,286 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\migrations\__init__.py first seen with mtime 1733578445.0
2025-06-19 15:35:10,286 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_null_file.py first seen with mtime **********.843939
2025-06-19 15:35:10,286 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0009_alter_user_last_name_max_length.py first seen with mtime **********.7043734
2025-06-19 15:35:10,286 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\__init__.py first seen with mtime **********.3284492
2025-06-19 15:35:10,286 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_discriminated_union.py first seen with mtime **********.8454869
2025-06-19 15:35:10,287 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch_create_params.py first seen with mtime **********.2570195
2025-06-19 15:35:10,287 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_refusal.py first seen with mtime **********.3798888
2025-06-19 15:35:10,287 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_content_param.py first seen with mtime **********.2902007
2025-06-19 15:35:10,287 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\scope.py first seen with mtime **********.862948
2025-06-19 15:35:10,288 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_content_response.py first seen with mtime **********.4008884
2025-06-19 15:35:10,288 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\embedding.py first seen with mtime **********.2600193
2025-06-19 15:35:10,288 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_annotation_delta_event.py first seen with mtime **********.3838882
2025-06-19 15:35:10,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\__init__.py first seen with mtime **********.2339766
2025-06-19 15:35:10,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread_deleted.py first seen with mtime **********.2882006
2025-06-19 15:35:10,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_update_params.py first seen with mtime **********.3234491
2025-06-19 15:35:10,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\response_format_json_object.py first seen with mtime **********.3938882
2025-06-19 15:35:10,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_usage.py first seen with mtime **********.3848884
2025-06-19 15:35:10,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\middleware\clickjacking.py first seen with mtime **********.7087119
2025-06-19 15:35:10,290 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\__init__.py first seen with mtime **********.3199975
2025-06-19 15:35:10,290 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_streaming.py first seen with mtime **********.2089188
2025-06-19 15:35:10,290 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\__init__.py first seen with mtime **********.6003442
2025-06-19 15:35:10,290 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_truncate_event.py first seen with mtime **********.295267
2025-06-19 15:35:10,290 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\__init__.py first seen with mtime **********.9695818
2025-06-19 15:35:10,291 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_content_part_added_event.py first seen with mtime **********.3054254
2025-06-19 15:35:10,291 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\regexopt.py first seen with mtime **********.808808
2025-06-19 15:35:10,291 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch_list_params.py first seen with mtime **********.2570195
2025-06-19 15:35:10,291 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\__init__.py first seen with mtime **********.1194832
2025-06-19 15:35:10,292 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_content.py first seen with mtime **********.3728926
2025-06-19 15:35:10,292 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_eventloop.py first seen with mtime **********.6731365
2025-06-19 15:35:10,292 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch_error.py first seen with mtime **********.2570195
2025-06-19 15:35:10,292 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\__init__.py first seen with mtime **********.6681404
2025-06-19 15:35:10,292 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\function_parameters.py first seen with mtime **********.3968883
2025-06-19 15:35:10,293 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_model_construction.py first seen with mtime **********.8494825
2025-06-19 15:35:10,293 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_message_item.py first seen with mtime **********.3758929
2025-06-19 15:35:10,293 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_create_event_param.py first seen with mtime **********.3064454
2025-06-19 15:35:10,293 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\measure.py first seen with mtime **********.8579438
2025-06-19 15:35:10,293 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant.py first seen with mtime **********.2802007
2025-06-19 15:35:10,293 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_base_client.py first seen with mtime **********.204862
2025-06-19 15:35:10,294 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_resource.py first seen with mtime **********.2089188
2025-06-19 15:35:10,294 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\input_item_list_params.py first seen with mtime **********.3568888
2025-06-19 15:35:10,294 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_multi_modal_input_param.py first seen with mtime **********.2672012
2025-06-19 15:35:10,295 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\vector_stores\files.py first seen with mtime **********.2540193
2025-06-19 15:35:10,295 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\distro\__init__.py first seen with mtime **********.0754955
2025-06-19 15:35:10,296 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\job_list_events_params.py first seen with mtime **********.3518999
2025-06-19 15:35:10,296 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\refusal_content_block.py first seen with mtime **********.3234491
2025-06-19 15:35:10,296 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\__init__.py first seen with mtime **********.6731365
2025-06-19 15:35:10,297 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\vector_stores\vector_stores.py first seen with mtime **********.2550194
2025-06-19 15:35:10,297 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\code_interpreter_tool_param.py first seen with mtime **********.2852006
2025-06-19 15:35:10,297 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0007_alter_validators_add_error_messages.py first seen with mtime **********.7033734
2025-06-19 15:35:10,298 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_server_event.py first seen with mtime **********.3034227
2025-06-19 15:35:10,298 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\vector_stores\__init__.py first seen with mtime **********.2530196
2025-06-19 15:35:10,298 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_updated_event.py first seen with mtime **********.3123617
2025-06-19 15:35:10,298 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run.py first seen with mtime **********.3244493
2025-06-19 15:35:10,298 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\types.py first seen with mtime **********.8664825
2025-06-19 15:35:10,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\generic\websocket.py first seen with mtime **********.109586
2025-06-19 15:35:10,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\translation_create_response.py first seen with mtime **********.2792006
2025-06-19 15:35:10,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\padding.py first seen with mtime **********.8579438
2025-06-19 15:35:10,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_generate_schema.py first seen with mtime **********.8474824
2025-06-19 15:35:10,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\_deltas.py first seen with mtime **********.2309766
2025-06-19 15:35:10,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice.py first seen with mtime **********.2832005
2025-06-19 15:35:10,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_create_params.py first seen with mtime **********.2752008
2025-06-19 15:35:10,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completion_create_params.py first seen with mtime **********.346893
2025-06-19 15:35:10,300 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_streams.py first seen with mtime **********.6801903
2025-06-19 15:35:10,300 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\config.py first seen with mtime **********.8324826
2025-06-19 15:35:10,300 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_verbose.py first seen with mtime **********.2772007
2025-06-19 15:35:10,300 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\lexer.py first seen with mtime **********.8078082
2025-06-19 15:35:10,301 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_created_event.py first seen with mtime **********.3113666
2025-06-19 15:35:10,301 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_version.py first seen with mtime **********.2240448
2025-06-19 15:35:10,301 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\middleware\common.py first seen with mtime **********.7097118
2025-06-19 15:35:10,301 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_option_param.py first seen with mtime **********.2842007
2025-06-19 15:35:10,301 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\aliases.py first seen with mtime **********.8303034
2025-06-19 15:35:10,302 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\runs\__init__.py first seen with mtime **********.2450187
2025-06-19 15:35:10,302 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\auto_file_chunking_strategy_param.py first seen with mtime **********.2560194
2025-06-19 15:35:10,302 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\uploads\parts.py first seen with mtime **********.2530196
2025-06-19 15:35:10,302 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic_core\_pydantic_core.cp311-win_amd64.pyd first seen with mtime **********.5570047
2025-06-19 15:35:10,302 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\abc.py first seen with mtime **********.0
2025-06-19 15:35:10,302 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_web_search_call_searching_event.py first seen with mtime **********.385888
2025-06-19 15:35:10,303 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\beta.py first seen with mtime **********.2410185
2025-06-19 15:35:10,303 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\streams\tls.py first seen with mtime **********.68319
2025-06-19 15:35:10,303 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_user_message_param.py first seen with mtime **********.3458936
2025-06-19 15:35:10,304 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_state.py first seen with mtime **********.2230449
2025-06-19 15:35:10,304 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_input_audio_transcription_delta_event.py first seen with mtime **********.293202
2025-06-19 15:35:10,304 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread_update_params.py first seen with mtime **********.2882006
2025-06-19 15:35:10,304 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_namespace_utils.py first seen with mtime **********.8494825
2025-06-19 15:35:10,304 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\__init__.py first seen with mtime **********.353893
2025-06-19 15:35:10,304 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_audio_delta_event.py first seen with mtime **********.3578937
2025-06-19 15:35:10,305 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\styled.py first seen with mtime **********.8659437
2025-06-19 15:35:10,305 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_developer_message_param.py first seen with mtime **********.3398929
2025-06-19 15:35:10,305 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0012_alter_user_first_name_max_length.py first seen with mtime **********.7053769
2025-06-19 15:35:10,305 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_stream_event.py first seen with mtime **********.3828883
2025-06-19 15:35:10,305 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\function_tool.py first seen with mtime **********.355893
2025-06-19 15:35:10,306 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_created_event.py first seen with mtime **********.2892005
2025-06-19 15:35:10,306 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\__init__.py first seen with mtime **********.5972836
2025-06-19 15:35:10,306 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_stream_options_param.py first seen with mtime **********.343893
2025-06-19 15:35:10,306 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job_event.py first seen with mtime **********.3498929
2025-06-19 15:35:10,306 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_api.py first seen with mtime **********.5942636
2025-06-19 15:35:10,306 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\speech_create_params.py first seen with mtime **********.2742007
2025-06-19 15:35:10,307 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_segment.py first seen with mtime **********.2762005
2025-06-19 15:35:10,307 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\message_creation_step_details.py first seen with mtime **********.331889
2025-06-19 15:35:10,307 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\errors.py first seen with mtime **********.8344827
2025-06-19 15:35:10,307 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_append_event.py first seen with mtime **********.2973366
2025-06-19 15:35:10,307 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\backends\mysql\compiler.py first seen with mtime **********.6318858
2025-06-19 15:35:10,308 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\regex\_regex.cp311-win_amd64.pyd first seen with mtime **********.8961744
2025-06-19 15:35:10,308 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_update_event.py first seen with mtime **********.3113666
2025-06-19 15:35:10,308 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_abnf.py first seen with mtime **********.2210486
2025-06-19 15:35:10,309 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_in_progress_event.py first seen with mtime **********.3718927
2025-06-19 15:35:10,309 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\jobs\jobs.py first seen with mtime **********.2500196
2025-06-19 15:35:10,309 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_qs.py first seen with mtime **********.207919
2025-06-19 15:35:10,310 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\rate_limits_updated_event.py first seen with mtime **********.300424
2025-06-19 15:35:10,310 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\file_citation_annotation.py first seen with mtime **********.3154497
2025-06-19 15:35:10,310 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_api.py first seen with mtime **********.1194832
2025-06-19 15:35:10,310 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_commit_event_param.py first seen with mtime **********.2994251
2025-06-19 15:35:10,311 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\tools.py first seen with mtime **********.8664825
2025-06-19 15:35:10,311 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file.py first seen with mtime **********.3164494
2025-06-19 15:35:10,311 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\__version__.py first seen with mtime **********.1194832
2025-06-19 15:35:10,312 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_models.py first seen with mtime **********.5952597
2025-06-19 15:35:10,312 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_item_list.py first seen with mtime **********.377889
2025-06-19 15:35:10,312 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\http11.py first seen with mtime **********.5983443
2025-06-19 15:35:10,313 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_testing.py first seen with mtime **********.67819
2025-06-19 15:35:10,313 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\upload_create_params.py first seen with mtime **********.2712011
2025-06-19 15:35:10,313 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\__init__.py first seen with mtime **********.8058078
2025-06-19 15:35:10,313 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0010_alter_group_name_max_length.py first seen with mtime **********.7043734
2025-06-19 15:35:10,314 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\all_models.py first seen with mtime **********.3898883
2025-06-19 15:35:10,314 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\__init__.py first seen with mtime **********.3488927
2025-06-19 15:35:10,314 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\layers.py first seen with mtime **********.1065862
2025-06-19 15:35:10,314 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_headers.py first seen with mtime **********.2220497
2025-06-19 15:35:10,315 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\dataclasses.py first seen with mtime **********.8604825
2025-06-19 15:35:10,315 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\color.py first seen with mtime **********.8499389
2025-06-19 15:35:10,316 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_models.py first seen with mtime **********.1235194
2025-06-19 15:35:10,316 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\auth.py first seen with mtime **********.1045432
2025-06-19 15:35:10,317 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\chat_model.py first seen with mtime **********.3898883
2025-06-19 15:35:10,317 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_palettes.py first seen with mtime **********.844939
2025-06-19 15:35:10,317 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\compound_filter.py first seen with mtime **********.395888
2025-06-19 15:35:10,317 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\chat\__init__.py first seen with mtime **********.2410185
2025-06-19 15:35:10,317 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\jobs\__init__.py first seen with mtime **********.24902
2025-06-19 15:35:10,318 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\version.py first seen with mtime **********.8684824
2025-06-19 15:35:10,318 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_urls.py first seen with mtime **********.1255639
2025-06-19 15:35:10,318 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_create_event.py first seen with mtime **********.3064454
2025-06-19 15:35:10,318 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\routing.py first seen with mtime **********.1075864
2025-06-19 15:35:10,319 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread_create_params.py first seen with mtime **********.2882006
2025-06-19 15:35:10,319 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_util.py first seen with mtime **********.2240448
2025-06-19 15:35:10,319 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_utils.py first seen with mtime **********.1255639
2025-06-19 15:35:10,319 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\colorsys.py first seen with mtime **********.0
2025-06-19 15:35:10,319 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\typing.py first seen with mtime **********.8674824
2025-06-19 15:35:10,320 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\main.py first seen with mtime **********.8374825
2025-06-19 15:35:10,320 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_auth.py first seen with mtime **********.120479
2025-06-19 15:35:10,320 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_types.py first seen with mtime **********.2099187
2025-06-19 15:35:10,321 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_fields.py first seen with mtime **********.8464828
2025-06-19 15:35:10,321 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_delete_event_param.py first seen with mtime **********.2922018
2025-06-19 15:35:10,321 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\static_file_chunking_strategy_object.py first seen with mtime **********.2692013
2025-06-19 15:35:10,321 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_signals.py first seen with mtime **********.6751904
2025-06-19 15:35:10,322 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_image_param.py first seen with mtime **********.373893
2025-06-19 15:35:10,322 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job_integration.py first seen with mtime **********.3498929
2025-06-19 15:35:10,322 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\realtime\__init__.py first seen with mtime **********.2420187
2025-06-19 15:35:10,322 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0004_alter_user_username_opts.py first seen with mtime **********.7021997
2025-06-19 15:35:10,322 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat_model.py first seen with mtime **********.2580192
2025-06-19 15:35:10,323 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_status_codes.py first seen with mtime **********.124564
2025-06-19 15:35:10,323 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completions\__init__.py first seen with mtime **********.3488927
2025-06-19 15:35:10,323 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\uploads\upload_part.py first seen with mtime **********.3998883
2025-06-19 15:35:10,323 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\file_search_tool.py first seen with mtime **********.286201
2025-06-19 15:35:10,323 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\contenttypes\migrations\0002_remove_content_type_name.py first seen with mtime **********.8052022
2025-06-19 15:35:10,324 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\containers.py first seen with mtime **********.8509388
2025-06-19 15:35:10,324 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\filters\__init__.py first seen with mtime **********.8118074
2025-06-19 15:35:10,324 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool.py first seen with mtime **********.3868883
2025-06-19 15:35:10,324 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_config.py first seen with mtime **********.121479
2025-06-19 15:35:10,324 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_itertools.py first seen with mtime **********.0
2025-06-19 15:35:10,324 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_function_call_option_param.py first seen with mtime **********.3398929
2025-06-19 15:35:10,325 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\_common.py first seen with mtime **********.0
2025-06-19 15:35:10,325 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_tasks.py first seen with mtime **********.6801903
2025-06-19 15:35:10,325 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\models.py first seen with mtime **********.2370188
2025-06-19 15:35:10,326 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\auto.py first seen with mtime **********.6013443
2025-06-19 15:35:10,326 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_created_event.py first seen with mtime **********.291201
2025-06-19 15:35:10,326 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_migration.py first seen with mtime **********.8303034
2025-06-19 15:35:10,326 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_audio.py first seen with mtime **********.336893
2025-06-19 15:35:10,326 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_chunking_strategy_param.py first seen with mtime **********.261019
2025-06-19 15:35:10,326 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_version.py first seen with mtime **********.2099187
2025-06-19 15:35:10,327 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_speech_started_event.py first seen with mtime **********.2994251
2025-06-19 15:35:10,327 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\__init__.py first seen with mtime **********.2400205
2025-06-19 15:35:10,327 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\abc.py first seen with mtime **********.847939
2025-06-19 15:35:10,327 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\__init__.py first seen with mtime **********.1265635
2025-06-19 15:35:10,327 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\migrations\__init__.py first seen with mtime **********.4805906
2025-06-19 15:35:10,327 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\jobs\fine_tuning_job_checkpoint.py first seen with mtime **********.3528886
2025-06-19 15:35:10,328 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\admin\migrations\0002_logentry_remove_auto_add.py first seen with mtime **********.4056838
2025-06-19 15:35:10,328 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\__init__.py first seen with mtime **********.3358927
2025-06-19 15:35:10,328 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_content.py first seen with mtime **********.2902007
2025-06-19 15:35:10,328 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread_create_and_run_params.py first seen with mtime **********.2872007
2025-06-19 15:35:10,329 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\__init__.py first seen with mtime **********.3948882
2025-06-19 15:35:10,329 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_create_response.py first seen with mtime **********.2660847
2025-06-19 15:35:10,329 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_audio_transcript_delta_event.py first seen with mtime **********.3588927
2025-06-19 15:35:10,329 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completion_list_params.py first seen with mtime **********.346893
2025-06-19 15:35:10,330 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\code_interpreter_tool_call.py first seen with mtime **********.3297408
2025-06-19 15:35:10,330 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_content_param.py first seen with mtime **********.3728926
2025-06-19 15:35:10,330 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\regex\regex.py first seen with mtime **********.8971746
2025-06-19 15:35:10,330 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\schema.py first seen with mtime **********.8664825
2025-06-19 15:35:10,330 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_proxy.py first seen with mtime **********.2139187
2025-06-19 15:35:10,331 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_chunking_strategy.py first seen with mtime **********.261019
2025-06-19 15:35:10,331 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_main.py first seen with mtime **********.1235194
2025-06-19 15:35:10,331 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_writers.py first seen with mtime **********.2240448
2025-06-19 15:35:10,331 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\transcriptions.py first seen with mtime **********.2390192
2025-06-19 15:35:10,332 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\filter.py first seen with mtime **********.8078082
2025-06-19 15:35:10,332 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\plugin\_schema_validator.py first seen with mtime **********.8574824
2025-06-19 15:35:10,332 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_tool_message_param.py first seen with mtime **********.344893
2025-06-19 15:35:10,332 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_create_response.py first seen with mtime **********.2752008
2025-06-19 15:35:10,333 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_failed_event.py first seen with mtime **********.365893
2025-06-19 15:35:10,333 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\validators.py first seen with mtime **********.8684824
2025-06-19 15:35:10,333 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\_legacy.py first seen with mtime **********.0
2025-06-19 15:35:10,333 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\align.py first seen with mtime **********.847939
2025-06-19 15:35:10,334 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\gpt_CWSC\routing.py first seen with mtime **********.0
2025-06-19 15:35:10,334 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_truncate_event_param.py first seen with mtime **********.295267
2025-06-19 15:35:10,334 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_audio_done_event.py first seen with mtime **********.3578937
2025-06-19 15:35:10,335 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\json_schema.py first seen with mtime **********.8364825
2025-06-19 15:35:10,335 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_retrieve_event.py first seen with mtime **********.2942014
2025-06-19 15:35:10,335 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\batches.py first seen with mtime **********.2349768
2025-06-19 15:35:10,335 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_refusal_param.py first seen with mtime **********.3798888
2025-06-19 15:35:10,336 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0002_delete_userinfo.py first seen with mtime **********.0
2025-06-19 15:35:10,336 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\emoji.py first seen with mtime **********.852939
2025-06-19 15:35:10,336 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\chat.py first seen with mtime **********.2470188
2025-06-19 15:35:10,336 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_param.py first seen with mtime **********.3184493
2025-06-19 15:35:10,337 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\web_search_tool_param.py first seen with mtime **********.3888881
2025-06-19 15:35:10,337 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\protocol.py first seen with mtime **********.860944
2025-06-19 15:35:10,337 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_list_params.py first seen with mtime **********.3224494
2025-06-19 15:35:10,337 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\completion.py first seen with mtime **********.2580192
2025-06-19 15:35:10,338 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\sessions.py first seen with mtime **********.1075864
2025-06-19 15:35:10,338 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\translation_create_params.py first seen with mtime **********.2782006
2025-06-19 15:35:10,338 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_text_delta_event.py first seen with mtime **********.309369
2025-06-19 15:35:10,338 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_content.py first seen with mtime **********.261019
2025-06-19 15:35:10,338 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\_tiktoken.cp311-win_amd64.pyd first seen with mtime **********.9795818
2025-06-19 15:35:10,339 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_audio_param.py first seen with mtime **********.336893
2025-06-19 15:35:10,339 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_item.py first seen with mtime **********.3768888
2025-06-19 15:35:10,339 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\conf\urls\__init__.py first seen with mtime **********.2256448
2025-06-19 15:35:10,339 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\metadata.py first seen with mtime **********.3928883
2025-06-19 15:35:10,340 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\models\sql\compiler.py first seen with mtime **********.6684542
2025-06-19 15:35:10,340 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\realtime\sessions.py first seen with mtime **********.2430186
2025-06-19 15:35:10,340 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_output_item_done_event.py first seen with mtime **********.308454
2025-06-19 15:35:10,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_export_format.py first seen with mtime **********.841953
2025-06-19 15:35:10,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_fileio.py first seen with mtime **********.6741648
2025-06-19 15:35:10,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\pretty.py first seen with mtime **********.8589437
2025-06-19 15:35:10,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_ssl.py first seen with mtime **********.5952597
2025-06-19 15:35:10,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\middleware\security.py first seen with mtime **********.7107117
2025-06-19 15:35:10,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_generics.py first seen with mtime **********.8474824
2025-06-19 15:35:10,342 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\reasoning_effort.py first seen with mtime **********.3928883
2025-06-19 15:35:10,342 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\step_list_params.py first seen with mtime **********.3338926
2025-06-19 15:35:10,342 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\_common.py first seen with mtime **********.2119188
2025-06-19 15:35:10,343 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_format_text_config_param.py first seen with mtime **********.3678927
2025-06-19 15:35:10,343 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_file.py first seen with mtime **********.3728926
2025-06-19 15:35:10,343 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_exceptions.py first seen with mtime **********.6741648
2025-06-19 15:35:10,343 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_streams.py first seen with mtime **********.6761901
2025-06-19 15:35:10,343 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_update_event_param.py first seen with mtime **********.3113666
2025-06-19 15:35:10,344 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\utils.py first seen with mtime **********.108586
2025-06-19 15:35:10,344 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\migrations\executor.py first seen with mtime **********.6474533
2025-06-19 15:35:10,344 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\__init__.py first seen with mtime **********.2550194
2025-06-19 15:35:10,345 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\refusal_delta_block.py first seen with mtime **********.3234491
2025-06-19 15:35:10,345 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_refusal_done_event.py first seen with mtime **********.3818884
2025-06-19 15:35:10,345 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call_output_screenshot_param.py first seen with mtime **********.362893
2025-06-19 15:35:10,346 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step_delta_event.py first seen with mtime **********.3328934
2025-06-19 15:35:10,346 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0001_initial.py first seen with mtime **********.701161
2025-06-19 15:35:10,346 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\color.py first seen with mtime **********.8594823
2025-06-19 15:35:10,346 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_files.py first seen with mtime **********.2069192
2025-06-19 15:35:10,347 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_param.py first seen with mtime **********.3758929
2025-06-19 15:35:10,347 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\response_format_json_object.py first seen with mtime **********.3978882
2025-06-19 15:35:10,347 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\speech.py first seen with mtime **********.238019
2025-06-19 15:35:10,347 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_system_message_param.py first seen with mtime **********.343893
2025-06-19 15:35:10,347 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_receivebuffer.py first seen with mtime **********.2230449
2025-06-19 15:35:10,348 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_content_part_done_event.py first seen with mtime **********.3064454
2025-06-19 15:35:10,348 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_response.py first seen with mtime **********.3024244
2025-06-19 15:35:10,348 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image_model.py first seen with mtime **********.264019
2025-06-19 15:35:10,348 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\function_tool_param.py first seen with mtime **********.355893
2025-06-19 15:35:10,348 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_content_part_param.py first seen with mtime **********.3214495
2025-06-19 15:35:10,349 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_signature.py first seen with mtime **********.8514824
2025-06-19 15:35:10,349 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_list_params.py first seen with mtime **********.2812006
2025-06-19 15:35:10,349 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\type_adapter.py first seen with mtime **********.8394818
2025-06-19 15:35:10,349 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job_wandb_integration.py first seen with mtime **********.3498929
2025-06-19 15:35:10,350 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\connection.py first seen with mtime **********.5972836
2025-06-19 15:35:10,350 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\tool_call_delta_object.py first seen with mtime **********.334893
2025-06-19 15:35:10,350 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\lowlevel.py first seen with mtime **********.669136
2025-06-19 15:35:10,350 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_internal_dataclass.py first seen with mtime **********.8484826
2025-06-19 15:35:10,351 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\__init__.py first seen with mtime **********.8584824
2025-06-19 15:35:10,351 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\tools\update_content.py first seen with mtime 1733578445.0
2025-06-19 15:35:10,351 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\responses\_responses.py first seen with mtime **********.2339766
2025-06-19 15:35:10,351 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_option.py first seen with mtime **********.2842007
2025-06-19 15:35:10,352 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_cancel_event.py first seen with mtime **********.3054254
2025-06-19 15:35:10,352 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\plugin\__init__.py first seen with mtime **********.8564825
2025-06-19 15:35:10,352 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_function_call_arguments_delta_event.py first seen with mtime **********.3074307
2025-06-19 15:35:10,352 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_param.py first seen with mtime **********.2842007
2025-06-19 15:35:10,353 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\abc.py first seen with mtime **********.0
2025-06-19 15:35:10,353 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_function_message_param.py first seen with mtime **********.3398929
2025-06-19 15:35:10,353 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_content_block.py first seen with mtime **********.317449
2025-06-19 15:35:10,353 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_tool_call.py first seen with mtime **********.3608942
2025-06-19 15:35:10,354 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\reasoning.py first seen with mtime **********.3928883
2025-06-19 15:35:10,354 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0003_initial.py first seen with mtime **********.0
2025-06-19 15:35:10,354 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_delta_block.py first seen with mtime **********.3184493
2025-06-19 15:35:10,355 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\embedding_model.py first seen with mtime **********.261019
2025-06-19 15:35:10,355 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\configparser.py first seen with mtime **********.0
2025-06-19 15:35:10,355 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_config.py first seen with mtime **********.3838882
2025-06-19 15:35:10,356 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_wrap.py first seen with mtime **********.846939
2025-06-19 15:35:10,356 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step_include.py first seen with mtime **********.3338926
2025-06-19 15:35:10,356 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\errors.py first seen with mtime **********.852939
2025-06-19 15:35:10,356 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\code_interpreter_tool.py first seen with mtime **********.2852006
2025-06-19 15:35:10,356 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\file_search_tool_call_delta.py first seen with mtime **********.330889
2025-06-19 15:35:10,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\file_search_tool_param.py first seen with mtime **********.355893
2025-06-19 15:35:10,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\comparison_filter.py first seen with mtime **********.3908885
2025-06-19 15:35:10,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\default.py first seen with mtime **********.1275637
2025-06-19 15:35:10,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\__init__.py first seen with mtime **********.2440188
2025-06-19 15:35:10,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\responses\__init__.py first seen with mtime **********.232977
2025-06-19 15:35:10,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\zstandard\__init__.py first seen with mtime 1742633606.8067648
2025-06-19 15:35:10,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_client.py first seen with mtime **********.121479
2025-06-19 15:35:10,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\default_styles.py first seen with mtime **********.851939
2025-06-19 15:35:10,358 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\migrations\0001_initial.py first seen with mtime 1733578445.0
2025-06-19 15:35:10,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_file_param.py first seen with mtime **********.373893
2025-06-19 15:35:10,359 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\class_validators.py first seen with mtime **********.8594823
2025-06-19 15:35:10,359 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\file_path_delta_annotation.py first seen with mtime **********.3164494
2025-06-19 15:35:10,360 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_delta_block.py first seen with mtime **********.3194492
2025-06-19 15:35:10,360 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\file_proxy.py first seen with mtime **********.852939
2025-06-19 15:35:10,360 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\easy_input_message_param.py first seen with mtime **********.3548927
2025-06-19 15:35:10,361 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\static_file_chunking_strategy_object_param.py first seen with mtime **********.2692013
2025-06-19 15:35:10,361 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\sync.py first seen with mtime **********.6023443
2025-06-19 15:35:10,361 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_create_params.py first seen with mtime **********.3244493
2025-06-19 15:35:10,361 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\datetime_parse.py first seen with mtime **********.8614824
2025-06-19 15:35:10,362 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_param.py first seen with mtime **********.2942014
2025-06-19 15:35:10,362 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_retrieve_params.py first seen with mtime **********.3818884
2025-06-19 15:35:10,362 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_parsing\_responses.py first seen with mtime **********.2299788
2025-06-19 15:35:10,362 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_create_params.py first seen with mtime **********.2802007
2025-06-19 15:35:10,363 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_schema_generation_shared.py first seen with mtime **********.8504827
2025-06-19 15:35:10,363 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_collections.py first seen with mtime **********.0
2025-06-19 15:35:10,363 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_logs.py first seen with mtime **********.2139187
2025-06-19 15:35:10,363 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\translation.py first seen with mtime **********.2782006
2025-06-19 15:35:10,364 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\response_format_text.py first seen with mtime **********.3988883
2025-06-19 15:35:10,364 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\parsed_response.py first seen with mtime **********.3568888
2025-06-19 15:35:10,364 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\lexers\__init__.py first seen with mtime **********.8178084
2025-06-19 15:35:10,364 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_tool_call_item.py first seen with mtime **********.369893
2025-06-19 15:35:10,364 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion.py first seen with mtime **********.3358927
2025-06-19 15:35:10,365 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_call_in_progress_event.py first seen with mtime **********.365893
2025-06-19 15:35:10,365 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\MySQLdb\cursors.py first seen with mtime 1742637949.6703618
2025-06-19 15:35:10,365 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_status.py first seen with mtime **********.3828883
2025-06-19 15:35:10,365 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\segment.py first seen with mtime **********.8639438
2025-06-19 15:35:10,366 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_format_text_json_schema_config_param.py first seen with mtime **********.368893
2025-06-19 15:35:10,366 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_web_search_call_completed_event.py first seen with mtime **********.385888
2025-06-19 15:35:10,366 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\uploads\__init__.py first seen with mtime **********.2520196
2025-06-19 15:35:10,366 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\themes.py first seen with mtime **********.8689482
2025-06-19 15:35:10,367 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_pydantic.py first seen with mtime **********.2269764
2025-06-19 15:35:10,367 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\moderations.py first seen with mtime **********.2370188
2025-06-19 15:35:10,367 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_created_event.py first seen with mtime **********.364893
2025-06-19 15:35:10,367 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\sniffio\__init__.py first seen with mtime 1742633606.9900455
2025-06-19 15:35:10,367 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\annotation.py first seen with mtime **********.3144498
2025-06-19 15:35:10,368 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\file_citation_delta_annotation.py first seen with mtime **********.3154497
2025-06-19 15:35:10,368 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\responses\_events.py first seen with mtime **********.232977
2025-06-19 15:35:10,368 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_parsing\__init__.py first seen with mtime **********.2289767
2025-06-19 15:35:10,368 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\embeddings.py first seen with mtime **********.2349768
2025-06-19 15:35:10,369 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\certifi\core.py first seen with mtime **********.4141934
2025-06-19 15:35:10,369 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_purpose.py first seen with mtime **********.2630188
2025-06-19 15:35:10,369 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_content.py first seen with mtime **********.3204496
2025-06-19 15:35:10,369 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\model.py first seen with mtime **********.9815776
2025-06-19 15:35:10,369 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\theme.py first seen with mtime **********.8679438
2025-06-19 15:35:10,370 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\audio.py first seen with mtime **********.238019
2025-06-19 15:35:10,370 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\contenttypes\migrations\0001_initial.py first seen with mtime **********.8042026
2025-06-19 15:35:10,370 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\parsed_chat_completion.py first seen with mtime **********.3478932
2025-06-19 15:35:10,370 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic_core\__init__.py first seen with mtime **********.5373993
2025-06-19 15:35:10,371 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_image.py first seen with mtime **********.373893
2025-06-19 15:35:10,371 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_tool_call.py first seen with mtime **********.3668928
2025-06-19 15:35:10,371 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_interpreting_event.py first seen with mtime **********.3608942
2025-06-19 15:35:10,372 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\http_proxy.py first seen with mtime **********.5993447
2025-06-19 15:35:10,372 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_completed_event.py first seen with mtime **********.3608942
2025-06-19 15:35:10,372 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completion_update_params.py first seen with mtime **********.346893
2025-06-19 15:35:10,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\__init__.py first seen with mtime **********.6033444
2025-06-19 15:35:10,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\function_parameters.py first seen with mtime **********.3918886
2025-06-19 15:35:10,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\MySQLdb\connections.py first seen with mtime 1742637949.669361
2025-06-19 15:35:10,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_create_params.py first seen with mtime **********.3638935
2025-06-19 15:35:10,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_content.py first seen with mtime **********.121479
2025-06-19 15:35:10,374 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\sounddevice_proxy.py first seen with mtime **********.2129188
2025-06-19 15:35:10,374 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\__init__.py first seen with mtime **********.2792006
2025-06-19 15:35:10,374 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\syntax.py first seen with mtime **********.8659437
2025-06-19 15:35:10,375 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_retrieve_event_param.py first seen with mtime **********.2942014
2025-06-19 15:35:10,375 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\migrations\0003_initial.py first seen with mtime 1733578445.0
2025-06-19 15:35:10,375 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\function_definition.py first seen with mtime **********.395888
2025-06-19 15:35:10,375 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call_output_screenshot.py first seen with mtime **********.3618937
2025-06-19 15:35:10,375 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_in_progress_event.py first seen with mtime **********.3598928
2025-06-19 15:35:10,376 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_reasoning_item.py first seen with mtime **********.3808885
2025-06-19 15:35:10,376 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\utils.py first seen with mtime **********.8674824
2025-06-19 15:35:10,376 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_sockets.py first seen with mtime **********.6791902
2025-06-19 15:35:10,377 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\styles\__init__.py first seen with mtime **********.9158113
2025-06-19 15:35:10,377 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\version.py first seen with mtime **********.210919
2025-06-19 15:35:10,377 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_done_event.py first seen with mtime **********.3848884
2025-06-19 15:35:10,377 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\__init__.py first seen with mtime **********.2892005
2025-06-19 15:35:10,377 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_audio_delta_event.py first seen with mtime **********.3034227
2025-06-19 15:35:10,377 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_function_call_arguments_done_event.py first seen with mtime **********.308454
2025-06-19 15:35:10,378 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_function_param.py first seen with mtime **********.2832005
2025-06-19 15:35:10,378 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text_content_block.py first seen with mtime **********.3264492
2025-06-19 15:35:10,378 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\config.py first seen with mtime **********.8594823
2025-06-19 15:35:10,379 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\http11.py first seen with mtime **********.6043444
2025-06-19 15:35:10,379 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_fileno.py first seen with mtime **********.842939
2025-06-19 15:35:10,380 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\repr.py first seen with mtime **********.862948
2025-06-19 15:35:10,380 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_readers.py first seen with mtime **********.2230449
2025-06-19 15:35:10,380 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\consumer.py first seen with mtime **********.1045432
2025-06-19 15:35:10,380 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\__init__.py first seen with mtime **********.238019
2025-06-19 15:35:10,380 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_param.py first seen with mtime **********.3888881
2025-06-19 15:35:10,381 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\__init__.py first seen with mtime **********.2299788
2025-06-19 15:35:10,381 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\version.py first seen with mtime **********.8424823
2025-06-19 15:35:10,381 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_word.py first seen with mtime **********.2782006
2025-06-19 15:35:10,381 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\responses_model.py first seen with mtime **********.3988883
2025-06-19 15:35:10,382 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\filecontrol\migrations\0003_alter_fileinfo_create_time_alter_fileinfo_size.py first seen with mtime 1733578442.0
2025-06-19 15:35:10,382 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\mock.py first seen with mtime **********.1275637
2025-06-19 15:35:10,382 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_code_done_event.py first seen with mtime **********.3598928
2025-06-19 15:35:10,382 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session_create_params.py first seen with mtime **********.3123617
2025-06-19 15:35:10,383 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\console.py first seen with mtime **********.8509388
2025-06-19 15:35:10,383 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\markup.py first seen with mtime **********.8569446
2025-06-19 15:35:10,383 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_subprocesses.py first seen with mtime **********.6761901
2025-06-19 15:35:10,383 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_connection.py first seen with mtime **********.2210486
2025-06-19 15:35:10,383 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_message_content_list_param.py first seen with mtime **********.374893
2025-06-19 15:35:10,384 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\parse.py first seen with mtime **********.8654823
2025-06-19 15:35:10,384 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session_update.py first seen with mtime **********.3134074
2025-06-19 15:35:10,384 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\errors.py first seen with mtime **********.8624823
2025-06-19 15:35:10,384 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0003_alter_user_email_max_length.py first seen with mtime **********.7021997
2025-06-19 15:35:10,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\chat\_completions.py first seen with mtime **********.2319767
2025-06-19 15:35:10,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_message_tool_call_param.py first seen with mtime **********.341893
2025-06-19 15:35:10,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_item_added_event.py first seen with mtime **********.377889
2025-06-19 15:35:10,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_web_search.py first seen with mtime **********.370893
2025-06-19 15:35:10,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\distro\distro.py first seen with mtime **********.0754955
2025-06-19 15:35:10,386 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\completion_create_params.py first seen with mtime **********.259019
2025-06-19 15:35:10,386 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_typing.py first seen with mtime **********.2159152
2025-06-19 15:35:10,386 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\__init__.py first seen with mtime **********.2210486
2025-06-19 15:35:10,386 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_message.py first seen with mtime **********.3408928
2025-06-19 15:35:10,387 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\realtime\transcription_sessions.py first seen with mtime **********.2440188
2025-06-19 15:35:10,387 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\wsgi.py first seen with mtime **********.1285636
2025-06-19 15:35:10,387 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_legacy_response.py first seen with mtime **********.2069192
2025-06-19 15:35:10,387 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_format_text_config.py first seen with mtime **********.3678927
2025-06-19 15:35:10,388 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_param.py first seen with mtime **********.2842007
2025-06-19 15:35:10,388 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_eventloop.py first seen with mtime **********.6791902
2025-06-19 15:35:10,585 - django.channels.server - WARNING - HTTP GET /static/pic/Wilson_Blade_v9.png 404 [0.01, 127.0.0.1:56545]
2025-06-19 15:35:10,597 - django.channels.server - INFO - WebSocket HANDSHAKING /ws/gpt/ [127.0.0.1:56548]
2025-06-19 15:35:10,624 - django.db.backends - DEBUG - (0.016) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:35:10,625 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:35:10,626 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:35:10.598277' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:35:10.598277', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:35:10,629 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:35:10,632 - django.channels.server - INFO - WebSocket CONNECT /ws/gpt/ [127.0.0.1:56548]
2025-06-19 15:35:11,824 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime **********.321
2025-06-19 15:35:11,827 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\session.py first seen with mtime **********.1464849
2025-06-19 15:35:11,843 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\cookie.py first seen with mtime **********.1464849
2025-06-19 15:35:11,846 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\context_processors.py first seen with mtime **********.5782743
2025-06-19 15:35:11,852 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\template\context_processors.py first seen with mtime **********.7127118
2025-06-19 15:35:11,871 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\context_processors.py first seen with mtime **********.1434848
2025-06-19 15:35:11,878 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\staticfiles\storage.py first seen with mtime **********.5785098
2025-06-19 15:35:11,886 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\serializers.py first seen with mtime **********.3189974
2025-06-19 15:35:11,887 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\fallback.py first seen with mtime **********.1464849
2025-06-19 15:35:29,280 - django.channels.server - INFO - HTTP GET /copywritting/ 200 [0.01, 127.0.0.1:56584]
2025-06-19 15:35:55,074 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:35:55,075 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:35:55,076 - django.db.backends - DEBUG - (0.016) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:35:55.067497' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:35:55.067497', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:35:55,080 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:35:56,716 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\encodings\idna.py first seen with mtime **********.0
2025-06-19 15:35:56,756 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\stringprep.py first seen with mtime **********.0
2025-06-19 15:35:57,233 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-19 15:36:09,040 - django.channels.server - INFO - HTTP POST /productinfo/ 200 [13.97, 127.0.0.1:56611]
2025-06-19 15:42:55,481 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index (2) first seen with mtime 1750318974.8597355
2025-06-19 15:42:55,483 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index (2)\chat.html first seen with mtime 1733578445.0
2025-06-19 15:42:55,483 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index (2)\checkCopywriting.html first seen with mtime 1733578445.0
2025-06-19 15:42:55,484 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index (2)\copywritting.html first seen with mtime 1733578446.0
2025-06-19 15:42:55,484 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index (2)\filecontrol.html first seen with mtime 1733578446.0
2025-06-19 15:42:55,484 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index (2)\main.html first seen with mtime 1750152109.1317265
2025-06-19 15:42:55,485 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index (2)\optimize.html first seen with mtime 1733578446.0
2025-06-19 15:42:55,485 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index (2)\usage.html first seen with mtime 1733578446.0
2025-06-19 15:46:35,130 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1733578446.0, current mtime: 1750319194.7684643
2025-06-19 15:46:35,137 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x000001EC6C624900>, True), (<function translation_file_changed at 0x000001EC6F3E3600>, None)].
2025-06-19 15:47:04,658 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750319194.7684643, current mtime: 1750319224.5747526
2025-06-19 15:47:04,660 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x000001EC6C624900>, True), (<function translation_file_changed at 0x000001EC6F3E3600>, None)].
2025-06-19 15:47:32,972 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750319224.5747526, current mtime: 1750319252.585603
2025-06-19 15:47:32,972 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x000001EC6C624900>, True), (<function translation_file_changed at 0x000001EC6F3E3600>, None)].
2025-06-19 15:47:51,568 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750319252.585603, current mtime: 1750319271.417755
2025-06-19 15:47:51,570 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x000001EC6C624900>, True), (<function translation_file_changed at 0x000001EC6F3E3600>, None)].
2025-06-19 15:48:20,073 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html previous mtime: 1750319271.417755, current mtime: 1750319298.976447
2025-06-19 15:48:20,075 - django.utils.autoreload - DEBUG - D:\硕士\aiagent\gpt_CWSC\templates\index\copywritting.html notified as changed. Signal results: [(<function template_changed at 0x000001EC6C624900>, True), (<function translation_file_changed at 0x000001EC6F3E3600>, None)].
2025-06-19 15:49:27,077 - daphne.server - INFO - HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-19 15:49:27,078 - daphne.server - INFO - Configuring endpoint tcp:port=8000:interface=127.0.0.1
2025-06-19 15:49:27,080 - daphne.server - INFO - Listening on TCP address 127.0.0.1:8000
2025-06-19 15:49:27,095 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool.py first seen with mtime **********.3868883
2025-06-19 15:49:27,095 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_created_event.py first seen with mtime **********.2892005
2025-06-19 15:49:27,095 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url.py first seen with mtime **********.3184493
2025-06-19 15:49:27,096 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_search_params.py first seen with mtime **********.2722006
2025-06-19 15:49:27,096 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_types.py first seen with mtime **********.2099187
2025-06-19 15:49:27,097 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_option.py first seen with mtime **********.2842007
2025-06-19 15:49:27,097 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\__init__.py first seen with mtime **********.2028618
2025-06-19 15:49:27,097 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_param.py first seen with mtime **********.3758929
2025-06-19 15:49:27,098 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_urls.py first seen with mtime **********.1255639
2025-06-19 15:49:27,098 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\response_format_text.py first seen with mtime **********.3988883
2025-06-19 15:49:27,099 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\tools\update_usage.py first seen with mtime 1733578445.0
2025-06-19 15:49:27,099 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_speech_stopped_event.py first seen with mtime **********.300424
2025-06-19 15:49:27,099 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_text.py first seen with mtime **********.3798888
2025-06-19 15:49:27,100 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\__init__.py first seen with mtime **********.2259765
2025-06-19 15:49:27,100 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\input_item_list_params.py first seen with mtime **********.3568888
2025-06-19 15:49:27,100 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\lexer.py first seen with mtime **********.8078082
2025-06-19 15:49:27,101 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\panel.py first seen with mtime **********.8589437
2025-06-19 15:49:27,101 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_assistant_message_param.py first seen with mtime **********.3358927
2025-06-19 15:49:27,101 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\themes.py first seen with mtime **********.8689482
2025-06-19 15:49:27,101 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_include.py first seen with mtime **********.2762005
2025-06-19 15:49:27,101 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_format_text_json_schema_config.py first seen with mtime **********.3678927
2025-06-19 15:49:27,102 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\vector_stores\file_batches.py first seen with mtime **********.2540193
2025-06-19 15:49:27,102 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\control.py first seen with mtime **********.851939
2025-06-19 15:49:27,102 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_text_param.py first seen with mtime **********.3808885
2025-06-19 15:49:27,103 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_append_event_param.py first seen with mtime **********.2973366
2025-06-19 15:49:27,103 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_resources.py first seen with mtime **********.6791902
2025-06-19 15:49:27,103 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\align.py first seen with mtime **********.847939
2025-06-19 15:49:27,103 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\certifi\core.py first seen with mtime **********.4141934
2025-06-19 15:49:27,104 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\step_list_params.py first seen with mtime **********.3338926
2025-06-19 15:49:27,105 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\migrations\recorder.py first seen with mtime **********.6494534
2025-06-19 15:49:27,105 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\completion_choice.py first seen with mtime **********.2580192
2025-06-19 15:49:27,105 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_message_param.py first seen with mtime **********.3788888
2025-06-19 15:49:27,106 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\conf\urls\__init__.py first seen with mtime **********.2256448
2025-06-19 15:49:27,106 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\connection.py first seen with mtime **********.6033444
2025-06-19 15:49:27,106 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\base.py first seen with mtime **********.6013443
2025-06-19 15:49:27,106 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\responses_model.py first seen with mtime **********.3988883
2025-06-19 15:49:27,107 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\function_tool_param.py first seen with mtime **********.2872007
2025-06-19 15:49:27,107 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_content_part_done_event.py first seen with mtime **********.3064454
2025-06-19 15:49:27,108 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\typing.py first seen with mtime **********.8674824
2025-06-19 15:49:27,108 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completions\message_list_params.py first seen with mtime **********.3488927
2025-06-19 15:49:27,109 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_tool_call_param.py first seen with mtime **********.3668928
2025-06-19 15:49:27,109 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\message_creation_step_details.py first seen with mtime **********.331889
2025-06-19 15:49:27,109 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\jiter\jiter.cp311-win_amd64.pyd first seen with mtime **********.065094
2025-06-19 15:49:27,110 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_deleted.py first seen with mtime **********.262019
2025-06-19 15:49:27,110 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_format_text_json_schema_config_param.py first seen with mtime **********.368893
2025-06-19 15:49:27,110 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\comparison_filter.py first seen with mtime **********.3908885
2025-06-19 15:49:27,110 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\function_tool_call_delta.py first seen with mtime **********.330889
2025-06-19 15:49:27,111 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice.py first seen with mtime **********.2832005
2025-06-19 15:49:27,111 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\pager.py first seen with mtime **********.8579438
2025-06-19 15:49:27,111 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0006_require_contenttypes_0002.py first seen with mtime **********.7033734
2025-06-19 15:49:27,112 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\reasoning.py first seen with mtime **********.3968883
2025-06-19 15:49:27,112 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_api.py first seen with mtime **********.5942636
2025-06-19 15:49:27,112 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\completions.py first seen with mtime **********.2349768
2025-06-19 15:49:27,113 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\json.py first seen with mtime **********.8634825
2025-06-19 15:49:27,113 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\sync.py first seen with mtime **********.6023443
2025-06-19 15:49:27,114 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\readers.py first seen with mtime **********.0
2025-06-19 15:49:27,114 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\function_definition.py first seen with mtime **********.3918886
2025-06-19 15:49:27,114 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\regexopt.py first seen with mtime **********.808808
2025-06-19 15:49:27,115 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_client_event.py first seen with mtime **********.3014345
2025-06-19 15:49:27,118 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\translations.py first seen with mtime **********.2390192
2025-06-19 15:49:27,119 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session_updated_event.py first seen with mtime **********.3134074
2025-06-19 15:49:27,119 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\function_tool_call.py first seen with mtime **********.330889
2025-06-19 15:49:27,119 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_reflection.py first seen with mtime **********.2139187
2025-06-19 15:49:27,120 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\http_proxy.py first seen with mtime **********.5993447
2025-06-19 15:49:27,120 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_spinners.py first seen with mtime **********.8459394
2025-06-19 15:49:27,120 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_committed_event.py first seen with mtime **********.2994251
2025-06-19 15:49:27,121 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_repr.py first seen with mtime **********.8504827
2025-06-19 15:49:27,121 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_synchronization.py first seen with mtime **********.6761901
2025-06-19 15:49:27,122 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_tool_call_item.py first seen with mtime **********.369893
2025-06-19 15:49:27,122 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_message.py first seen with mtime **********.3408928
2025-06-19 15:49:27,123 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_response_format_option.py first seen with mtime **********.2812006
2025-06-19 15:49:27,123 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\distro\distro.py first seen with mtime **********.0754955
2025-06-19 15:49:27,123 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_update_params.py first seen with mtime **********.3234491
2025-06-19 15:49:27,123 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\json_schema.py first seen with mtime **********.8364825
2025-06-19 15:49:27,124 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\routing.py first seen with mtime **********.1075864
2025-06-19 15:49:27,124 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\plugin.py first seen with mtime **********.808808
2025-06-19 15:49:27,124 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\responses\_events.py first seen with mtime **********.232977
2025-06-19 15:49:27,124 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\code_interpreter_tool_call.py first seen with mtime **********.3297408
2025-06-19 15:49:27,125 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image.py first seen with mtime **********.2630188
2025-06-19 15:49:27,125 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_types.py first seen with mtime **********.3878887
2025-06-19 15:49:27,126 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_decoders.py first seen with mtime **********.122479
2025-06-19 15:49:27,126 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\__init__.py first seen with mtime **********.2742007
2025-06-19 15:49:27,126 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_typing.py first seen with mtime **********.2159152
2025-06-19 15:49:27,127 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_refusal_param.py first seen with mtime **********.338893
2025-06-19 15:49:27,127 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\errors.py first seen with mtime **********.8344827
2025-06-19 15:49:27,127 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\web_search_tool_param.py first seen with mtime **********.3888881
2025-06-19 15:49:27,128 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\vector_store_file_deleted.py first seen with mtime **********.4028885
2025-06-19 15:49:27,128 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_segment.py first seen with mtime **********.2762005
2025-06-19 15:49:27,128 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\styles\__init__.py first seen with mtime **********.9158113
2025-06-19 15:49:27,129 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\lowlevel.py first seen with mtime **********.669136
2025-06-19 15:49:27,129 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\mock.py first seen with mtime **********.6023443
2025-06-19 15:49:27,129 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\chat_model.py first seen with mtime **********.3948882
2025-06-19 15:49:27,130 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\annotated_types\__init__.py first seen with mtime **********.4265754
2025-06-19 15:49:27,130 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_create_params.py first seen with mtime **********.3638935
2025-06-19 15:49:27,131 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0008_alter_user_username_max_length.py first seen with mtime **********.7043734
2025-06-19 15:49:27,131 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_docs_extraction.py first seen with mtime **********.8454869
2025-06-19 15:49:27,131 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool.py first seen with mtime **********.2822006
2025-06-19 15:49:27,132 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_abnf.py first seen with mtime **********.2210486
2025-06-19 15:49:27,132 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_cancel_event_param.py first seen with mtime **********.3054254
2025-06-19 15:49:27,132 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_delta.py first seen with mtime **********.3224494
2025-06-19 15:49:27,132 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_update_event_param.py first seen with mtime **********.3113666
2025-06-19 15:49:27,133 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_config.py first seen with mtime **********.121479
2025-06-19 15:49:27,133 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_signals.py first seen with mtime **********.6751904
2025-06-19 15:49:27,133 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_code_done_event.py first seen with mtime **********.3598928
2025-06-19 15:49:27,133 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\all_models.py first seen with mtime **********.3898883
2025-06-19 15:49:27,134 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_input_audio_transcription_failed_event.py first seen with mtime **********.2942014
2025-06-19 15:49:27,134 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\admin\migrations\0001_initial.py first seen with mtime **********.4056838
2025-06-19 15:49:27,134 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session.py first seen with mtime **********.3123617
2025-06-19 15:49:27,134 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\response_format_json_object.py first seen with mtime **********.3938882
2025-06-19 15:49:27,135 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\chat\__init__.py first seen with mtime **********.2410185
2025-06-19 15:49:27,135 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\file_search_tool.py first seen with mtime **********.286201
2025-06-19 15:49:27,135 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_batch_create_params.py first seen with mtime **********.4008884
2025-06-19 15:49:27,135 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_web_search_call_searching_event.py first seen with mtime **********.385888
2025-06-19 15:49:27,136 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_resources.py first seen with mtime **********.6751904
2025-06-19 15:49:27,136 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_list_params.py first seen with mtime **********.2812006
2025-06-19 15:49:27,138 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\migrations\loader.py first seen with mtime **********.6484532
2025-06-19 15:49:27,138 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\easy_input_message_param.py first seen with mtime **********.3548927
2025-06-19 15:49:27,138 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion.py first seen with mtime **********.3358927
2025-06-19 15:49:27,138 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_content.py first seen with mtime **********.261019
2025-06-19 15:49:27,139 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\chat\_events.py first seen with mtime **********.2319767
2025-06-19 15:49:27,139 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image_generate_params.py first seen with mtime **********.264019
2025-06-19 15:49:27,139 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread.py first seen with mtime **********.2872007
2025-06-19 15:49:27,140 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\__init__.py first seen with mtime **********.0
2025-06-19 15:49:27,140 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\websocket_connection_options.py first seen with mtime **********.2732008
2025-06-19 15:49:27,140 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_tool_call_output_item.py first seen with mtime **********.369893
2025-06-19 15:49:27,141 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_content.py first seen with mtime **********.121479
2025-06-19 15:49:27,141 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\response_format_json_schema.py first seen with mtime **********.3938882
2025-06-19 15:49:27,141 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\_legacy.py first seen with mtime **********.0
2025-06-19 15:49:27,141 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\sniffio\_version.py first seen with mtime 1742633606.9900455
2025-06-19 15:49:27,142 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\reasoning_effort.py first seen with mtime **********.3978882
2025-06-19 15:49:27,142 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_message.py first seen with mtime **********.3788888
2025-06-19 15:49:27,142 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_schema_generation_shared.py first seen with mtime **********.8504827
2025-06-19 15:49:27,142 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\jobs\__init__.py first seen with mtime **********.24902
2025-06-19 15:49:27,143 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\__init__.py first seen with mtime **********.3948882
2025-06-19 15:49:27,143 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\__version__.py first seen with mtime **********.1194832
2025-06-19 15:49:27,143 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\code_interpreter_tool_param.py first seen with mtime **********.2852006
2025-06-19 15:49:27,143 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\jupyter.py first seen with mtime **********.854947
2025-06-19 15:49:27,144 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\table.py first seen with mtime **********.8669436
2025-06-19 15:49:27,144 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_created_event.py first seen with mtime **********.291201
2025-06-19 15:49:27,144 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_models.py first seen with mtime **********.2069192
2025-06-19 15:49:27,145 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\pandas_proxy.py first seen with mtime **********.2119188
2025-06-19 15:49:27,145 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\generic\__init__.py first seen with mtime **********.108586
2025-06-19 15:49:27,145 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\uploads\parts.py first seen with mtime **********.2530196
2025-06-19 15:49:27,145 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\palette.py first seen with mtime **********.8589437
2025-06-19 15:49:27,146 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_modality.py first seen with mtime **********.341893
2025-06-19 15:49:27,146 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_content_block_param.py first seen with mtime **********.317449
2025-06-19 15:49:27,146 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_response_status.py first seen with mtime **********.3024244
2025-06-19 15:49:27,146 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\networks.py first seen with mtime **********.8654823
2025-06-19 15:49:27,147 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\metadata.py first seen with mtime **********.3928883
2025-06-19 15:49:27,147 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_sync.py first seen with mtime **********.2149189
2025-06-19 15:49:27,147 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\_itertools.py first seen with mtime **********.0
2025-06-19 15:49:27,147 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription.py first seen with mtime **********.2752008
2025-06-19 15:49:27,148 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_audio_delta_event.py first seen with mtime **********.3034227
2025-06-19 15:49:27,148 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\sounddevice_proxy.py first seen with mtime **********.2129188
2025-06-19 15:49:27,149 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_delete_event_param.py first seen with mtime **********.2922018
2025-06-19 15:49:27,149 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_response.py first seen with mtime **********.2089188
2025-06-19 15:49:27,149 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_update_params.py first seen with mtime **********.2732008
2025-06-19 15:49:27,150 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\__init__.py first seen with mtime **********.2892005
2025-06-19 15:49:27,151 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\__init__.py first seen with mtime **********.3488927
2025-06-19 15:49:27,151 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\translation_create_params.py first seen with mtime **********.2782006
2025-06-19 15:49:27,152 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_trace.py first seen with mtime **********.5962598
2025-06-19 15:49:27,152 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image_create_variation_params.py first seen with mtime **********.2630188
2025-06-19 15:49:27,152 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_config_param.py first seen with mtime **********.3838882
2025-06-19 15:49:27,153 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_delta_event.py first seen with mtime **********.3848884
2025-06-19 15:49:27,153 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\embedding.py first seen with mtime **********.2600193
2025-06-19 15:49:27,153 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\interfaces.py first seen with mtime **********.5993447
2025-06-19 15:49:27,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\parse.py first seen with mtime **********.8654823
2025-06-19 15:49:27,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_format_text_config.py first seen with mtime **********.3678927
2025-06-19 15:49:27,154 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_list_params.py first seen with mtime **********.3224494
2025-06-19 15:49:27,155 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\step_retrieve_params.py first seen with mtime **********.3338926
2025-06-19 15:49:27,155 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\__init__.py first seen with mtime **********.3998883
2025-06-19 15:49:27,156 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_meta.py first seen with mtime **********.0
2025-06-19 15:49:27,156 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0004_alter_user_username_opts.py first seen with mtime **********.7021997
2025-06-19 15:49:27,156 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\tool_call_delta_object.py first seen with mtime **********.334893
2025-06-19 15:49:27,156 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file.py first seen with mtime **********.3164494
2025-06-19 15:49:27,156 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\_deltas.py first seen with mtime **********.2309766
2025-06-19 15:49:27,157 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_tool_param.py first seen with mtime **********.3458936
2025-06-19 15:49:27,157 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_create_params.py first seen with mtime **********.2802007
2025-06-19 15:49:27,157 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_web_search_call_in_progress_event.py first seen with mtime **********.385888
2025-06-19 15:49:27,157 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_file_param.py first seen with mtime **********.373893
2025-06-19 15:49:27,157 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\compound_filter.py first seen with mtime **********.3908885
2025-06-19 15:49:27,158 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\computer_tool_param.py first seen with mtime **********.3548927
2025-06-19 15:49:27,158 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\configparser.py first seen with mtime **********.0
2025-06-19 15:49:27,158 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch_list_params.py first seen with mtime **********.2570195
2025-06-19 15:49:27,158 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_itertools.py first seen with mtime **********.0
2025-06-19 15:49:27,158 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\connection_pool.py first seen with mtime **********.5983443
2025-06-19 15:49:27,159 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session_create_params.py first seen with mtime **********.3123617
2025-06-19 15:49:27,159 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_signature.py first seen with mtime **********.8514824
2025-06-19 15:49:27,159 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_function.py first seen with mtime **********.3868883
2025-06-19 15:49:27,159 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step_delta.py first seen with mtime **********.3328934
2025-06-19 15:49:27,159 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_connect_params.py first seen with mtime **********.3014345
2025-06-19 15:49:27,160 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\completions\completions.py first seen with mtime **********.248019
2025-06-19 15:49:27,160 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\__init__.py first seen with mtime **********.2470188
2025-06-19 15:49:27,160 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step_delta_message_delta.py first seen with mtime **********.3328934
2025-06-19 15:49:27,161 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\embedding_create_params.py first seen with mtime **********.2600193
2025-06-19 15:49:27,161 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job.py first seen with mtime **********.3498929
2025-06-19 15:49:27,161 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0003_alter_user_email_max_length.py first seen with mtime **********.7021997
2025-06-19 15:49:27,162 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_reasoning_item_param.py first seen with mtime **********.3808885
2025-06-19 15:49:27,162 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\live_render.py first seen with mtime **********.8559444
2025-06-19 15:49:27,163 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0005_usage_idx_userid_usetime.py first seen with mtime **********.0
2025-06-19 15:49:27,163 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_message_tool_call_param.py first seen with mtime **********.341893
2025-06-19 15:49:27,163 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\translation_verbose.py first seen with mtime **********.2792006
2025-06-19 15:49:27,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\color.py first seen with mtime **********.8499389
2025-06-19 15:49:27,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\refusal_content_block.py first seen with mtime **********.3234491
2025-06-19 15:49:27,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\console.py first seen with mtime **********.8509388
2025-06-19 15:49:27,164 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\rate_limits_updated_event.py first seen with mtime **********.300424
2025-06-19 15:49:27,165 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\runs\runs.py first seen with mtime **********.246019
2025-06-19 15:49:27,165 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\batches.py first seen with mtime **********.2349768
2025-06-19 15:49:27,165 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\__init__.py first seen with mtime **********.2400205
2025-06-19 15:49:27,166 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread_create_and_run_params.py first seen with mtime **********.2872007
2025-06-19 15:49:27,166 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_create_params.py first seen with mtime **********.2660847
2025-06-19 15:49:27,166 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\plugin\_loader.py first seen with mtime **********.8574824
2025-06-19 15:49:27,167 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_function_param.py first seen with mtime **********.3868883
2025-06-19 15:49:27,167 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\function_parameters.py first seen with mtime **********.3918886
2025-06-19 15:49:27,168 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_response_usage.py first seen with mtime **********.3024244
2025-06-19 15:49:27,168 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\speech_create_params.py first seen with mtime **********.2742007
2025-06-19 15:49:27,168 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\error_object.py first seen with mtime **********.3918886
2025-06-19 15:49:27,168 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_export_format.py first seen with mtime **********.841953
2025-06-19 15:49:27,168 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\core\asgi.py first seen with mtime **********.5846596
2025-06-19 15:49:27,168 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_interpreting_event.py first seen with mtime **********.3608942
2025-06-19 15:49:27,169 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\__init__.py first seen with mtime **********.2339766
2025-06-19 15:49:27,169 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_audio_transcript_done_event.py first seen with mtime **********.3588927
2025-06-19 15:49:27,169 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_serializers.py first seen with mtime **********.8504827
2025-06-19 15:49:27,170 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_readers.py first seen with mtime **********.2230449
2025-06-19 15:49:27,170 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_headers.py first seen with mtime **********.2220497
2025-06-19 15:49:27,170 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\realtime\__init__.py first seen with mtime **********.2420187
2025-06-19 15:49:27,170 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_content_block.py first seen with mtime **********.3194492
2025-06-19 15:49:27,171 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\code_interpreter_tool_call_delta.py first seen with mtime **********.3298812
2025-06-19 15:49:27,174 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\middleware\security.py first seen with mtime **********.7107117
2025-06-19 15:49:27,174 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_text_param.py first seen with mtime **********.338893
2025-06-19 15:49:27,174 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\gpt_CWSC\routing.py first seen with mtime **********.0
2025-06-19 15:49:27,175 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_cell_widths.py first seen with mtime **********.8409388
2025-06-19 15:49:27,175 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_proxy.py first seen with mtime **********.2139187
2025-06-19 15:49:27,175 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_api.py first seen with mtime **********.1194832
2025-06-19 15:49:27,176 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\views\csrf.py first seen with mtime **********.7457466
2025-06-19 15:49:27,176 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\__init__.py first seen with mtime **********.6681404
2025-06-19 15:49:27,176 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\admin\migrations\0002_logentry_remove_auto_add.py first seen with mtime **********.4056838
2025-06-19 15:49:27,177 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session.py first seen with mtime **********.309369
2025-06-19 15:49:27,177 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\comparison_filter.py first seen with mtime **********.395888
2025-06-19 15:49:27,177 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_wrap.py first seen with mtime **********.846939
2025-06-19 15:49:27,178 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_text_param.py first seen with mtime **********.3768888
2025-06-19 15:49:27,178 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0007_alter_validators_add_error_messages.py first seen with mtime **********.7033734
2025-06-19 15:49:27,179 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_subprocesses.py first seen with mtime **********.6801903
2025-06-19 15:49:27,179 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\chat\_completions.py first seen with mtime **********.2319767
2025-06-19 15:49:27,179 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\function_definition.py first seen with mtime **********.395888
2025-06-19 15:49:27,179 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\http11.py first seen with mtime **********.5983443
2025-06-19 15:49:27,180 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_tasks.py first seen with mtime **********.6801903
2025-06-19 15:49:27,180 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\class_validators.py first seen with mtime **********.8594823
2025-06-19 15:49:27,180 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_system_message_param.py first seen with mtime **********.343893
2025-06-19 15:49:27,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\base.py first seen with mtime **********.3199975
2025-06-19 15:49:27,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\job_list_params.py first seen with mtime **********.3518999
2025-06-19 15:49:27,181 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\parsed_response.py first seen with mtime **********.3568888
2025-06-19 15:49:27,182 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\cells.py first seen with mtime **********.848939
2025-06-19 15:49:27,182 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_param.py first seen with mtime **********.3204496
2025-06-19 15:49:27,182 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_audio_delta_event.py first seen with mtime **********.3578937
2025-06-19 15:49:27,183 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_state.py first seen with mtime **********.2230449
2025-06-19 15:49:27,183 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\__init__.py first seen with mtime **********.353893
2025-06-19 15:49:27,183 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\realtime\realtime.py first seen with mtime **********.2430186
2025-06-19 15:49:27,184 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_text_delta_event.py first seen with mtime **********.2772007
2025-06-19 15:49:27,184 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_with_reference.py first seen with mtime **********.296202
2025-06-19 15:49:27,184 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_input_audio_param.py first seen with mtime **********.3378928
2025-06-19 15:49:27,184 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_version.py first seen with mtime **********.2099187
2025-06-19 15:49:27,185 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\plugin\_schema_validator.py first seen with mtime **********.8574824
2025-06-19 15:49:27,185 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\aliases.py first seen with mtime **********.8303034
2025-06-19 15:49:27,185 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_streaming.py first seen with mtime **********.2089188
2025-06-19 15:49:27,185 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\anyio.py first seen with mtime **********.6003442
2025-06-19 15:49:27,186 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_types_param.py first seen with mtime **********.3878887
2025-06-19 15:49:27,186 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_fileno.py first seen with mtime **********.842939
2025-06-19 15:49:27,186 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_palettes.py first seen with mtime **********.844939
2025-06-19 15:49:27,187 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\__init__.py first seen with mtime **********.8399444
2025-06-19 15:49:27,187 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_qs.py first seen with mtime **********.207919
2025-06-19 15:49:27,187 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\moderations.py first seen with mtime **********.2370188
2025-06-19 15:49:27,187 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_function_call_arguments_done_event.py first seen with mtime **********.308454
2025-06-19 15:49:27,188 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_user_message_param.py first seen with mtime **********.3458936
2025-06-19 15:49:27,188 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\lexers\__init__.py first seen with mtime **********.8178084
2025-06-19 15:49:27,189 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_refusal.py first seen with mtime **********.3798888
2025-06-19 15:49:27,189 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call_output_screenshot_param.py first seen with mtime **********.362893
2025-06-19 15:49:27,189 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completions\__init__.py first seen with mtime **********.3488927
2025-06-19 15:49:27,189 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_multipart.py first seen with mtime **********.124564
2025-06-19 15:49:27,190 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\repr.py first seen with mtime **********.862948
2025-06-19 15:49:27,190 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\completion_create_params.py first seen with mtime **********.259019
2025-06-19 15:49:27,190 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_known_annotated_metadata.py first seen with mtime **********.8484826
2025-06-19 15:49:27,190 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\highlighter.py first seen with mtime **********.853939
2025-06-19 15:49:27,191 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_list_params.py first seen with mtime **********.2722006
2025-06-19 15:49:27,191 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\schema.py first seen with mtime **********.8664825
2025-06-19 15:49:27,191 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run.py first seen with mtime **********.3244493
2025-06-19 15:49:27,192 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\__init__.py first seen with mtime **********.2550194
2025-06-19 15:49:27,192 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_usage.py first seen with mtime **********.3848884
2025-06-19 15:49:27,192 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\base.py first seen with mtime **********.1265635
2025-06-19 15:49:27,192 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\functional_validators.py first seen with mtime **********.8354826
2025-06-19 15:49:27,193 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_completed_event.py first seen with mtime **********.3598928
2025-06-19 15:49:27,193 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\datetime_parse.py first seen with mtime **********.8614824
2025-06-19 15:49:27,193 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\migrations\graph.py first seen with mtime **********.6474533
2025-06-19 15:49:27,193 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_module_client.py first seen with mtime **********.207919
2025-06-19 15:49:27,194 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation.py first seen with mtime **********.266019
2025-06-19 15:49:27,194 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\upload_create_params.py first seen with mtime **********.2712011
2025-06-19 15:49:27,194 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\emoji.py first seen with mtime **********.852939
2025-06-19 15:49:27,195 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\constrain.py first seen with mtime **********.8509388
2025-06-19 15:49:27,195 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\__init__.py first seen with mtime **********.8424823
2025-06-19 15:49:27,195 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\core.py first seen with mtime **********.9805818
2025-06-19 15:49:27,195 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_files.py first seen with mtime **********.2069192
2025-06-19 15:49:27,196 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\vector_stores\vector_stores.py first seen with mtime **********.2550194
2025-06-19 15:49:27,196 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_emoji_replace.py first seen with mtime **********.841953
2025-06-19 15:49:27,196 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\middleware.py first seen with mtime **********.1065862
2025-06-19 15:49:27,196 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\middleware.py first seen with mtime **********.317997
2025-06-19 15:49:27,197 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_verbose.py first seen with mtime **********.2772007
2025-06-19 15:49:27,197 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_text_done_event.py first seen with mtime **********.309369
2025-06-19 15:49:27,197 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_typedattr.py first seen with mtime **********.67819
2025-06-19 15:49:27,198 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\region.py first seen with mtime **********.8619478
2025-06-19 15:49:27,199 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\readers.py first seen with mtime **********.0
2025-06-19 15:49:27,199 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\__init__.py first seen with mtime **********.3284492
2025-06-19 15:49:27,199 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_eventloop.py first seen with mtime **********.6791902
2025-06-19 15:49:27,200 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_completed_event.py first seen with mtime **********.3608942
2025-06-19 15:49:27,200 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_param.py first seen with mtime **********.3888881
2025-06-19 15:49:27,200 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_search_response.py first seen with mtime **********.2732008
2025-06-19 15:49:27,200 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\scope.py first seen with mtime **********.862948
2025-06-19 15:49:27,201 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_update_params.py first seen with mtime **********.4018884
2025-06-19 15:49:27,202 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_option_param.py first seen with mtime **********.2842007
2025-06-19 15:49:27,202 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\file_search_tool_call.py first seen with mtime **********.330889
2025-06-19 15:49:27,202 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\jobs\checkpoint_list_params.py first seen with mtime **********.3528886
2025-06-19 15:49:27,202 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_param.py first seen with mtime **********.3184493
2025-06-19 15:49:27,203 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\messages.py first seen with mtime **********.2440188
2025-06-19 15:49:27,203 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_stream_event.py first seen with mtime **********.2762005
2025-06-19 15:49:27,203 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\modeline.py first seen with mtime **********.808808
2025-06-19 15:49:27,204 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\jobs\checkpoints.py first seen with mtime **********.2500196
2025-06-19 15:49:27,204 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_message_tool_call.py first seen with mtime **********.3408928
2025-06-19 15:49:27,204 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_content_delta.py first seen with mtime **********.3214495
2025-06-19 15:49:27,204 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\numpy_proxy.py first seen with mtime **********.2119188
2025-06-19 15:49:27,205 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\regex\_regex.cp311-win_amd64.pyd first seen with mtime **********.8961744
2025-06-19 15:49:27,205 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_item.py first seen with mtime **********.377889
2025-06-19 15:49:27,205 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_utils.py first seen with mtime **********.8524857
2025-06-19 15:49:27,205 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\middleware\clickjacking.py first seen with mtime **********.7087119
2025-06-19 15:49:27,206 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_message_content_list_param.py first seen with mtime **********.374893
2025-06-19 15:49:27,206 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_message_item.py first seen with mtime **********.3758929
2025-06-19 15:49:27,207 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\text.py first seen with mtime **********.8679438
2025-06-19 15:49:27,207 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\terminal_theme.py first seen with mtime **********.8669436
2025-06-19 15:49:27,207 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_list_params.py first seen with mtime **********.4018884
2025-06-19 15:49:27,208 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\mock.py first seen with mtime **********.1275637
2025-06-19 15:49:27,208 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\error_event.py first seen with mtime **********.2973366
2025-06-19 15:49:27,208 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\annotated_types.py first seen with mtime **********.8584824
2025-06-19 15:49:27,209 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_clear_event.py first seen with mtime **********.298427
2025-06-19 15:49:27,209 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\completions\__init__.py first seen with mtime **********.2470188
2025-06-19 15:49:27,209 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_text.py first seen with mtime **********.3768888
2025-06-19 15:49:27,210 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_old_api.py first seen with mtime **********.2269764
2025-06-19 15:49:27,210 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_server_event.py first seen with mtime **********.3034227
2025-06-19 15:49:27,211 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\_adapters.py first seen with mtime **********.0
2025-06-19 15:49:27,211 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\embeddings.py first seen with mtime **********.2349768
2025-06-19 15:49:27,211 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_code_delta_event.py first seen with mtime **********.3588927
2025-06-19 15:49:27,212 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\tool_calls_step_details.py first seen with mtime **********.334893
2025-06-19 15:49:27,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\registry.py first seen with mtime **********.982582
2025-06-19 15:49:27,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text_content_block_param.py first seen with mtime **********.3274493
2025-06-19 15:49:27,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_emoji_codes.py first seen with mtime **********.841953
2025-06-19 15:49:27,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text_content_block.py first seen with mtime **********.3264492
2025-06-19 15:49:27,214 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\distro\__init__.py first seen with mtime **********.0754955
2025-06-19 15:49:27,215 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0002_alter_permission_name_max_length.py first seen with mtime **********.701161
2025-06-19 15:49:27,215 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\progress_bar.py first seen with mtime **********.8599439
2025-06-19 15:49:27,215 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\utils.py first seen with mtime **********.8674824
2025-06-19 15:49:27,216 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_response_format_option_param.py first seen with mtime **********.2812006
2025-06-19 15:49:27,216 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\auto.py first seen with mtime **********.6013443
2025-06-19 15:49:27,217 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_parsing\_completions.py first seen with mtime **********.2289767
2025-06-19 15:49:27,217 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\filecontrol\migrations\0002_alter_fileinfo_upload_user.py first seen with mtime 1733578442.0
2025-06-19 15:49:27,217 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\speech.py first seen with mtime **********.238019
2025-06-19 15:49:27,217 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_item_done_event.py first seen with mtime **********.3788888
2025-06-19 15:49:27,217 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_truncate_event.py first seen with mtime **********.295267
2025-06-19 15:49:27,218 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_create_response.py first seen with mtime **********.2660847
2025-06-19 15:49:27,218 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_decorators.py first seen with mtime **********.8444824
2025-06-19 15:49:27,218 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\_assistants.py first seen with mtime **********.2299788
2025-06-19 15:49:27,218 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\model.py first seen with mtime **********.9815776
2025-06-19 15:49:27,219 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_audio_transcript_delta_event.py first seen with mtime **********.3044255
2025-06-19 15:49:27,220 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_deleted.py first seen with mtime **********.2712011
2025-06-19 15:49:27,220 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_resource.py first seen with mtime **********.2089188
2025-06-19 15:49:27,220 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\jobs\fine_tuning_job_checkpoint.py first seen with mtime **********.3528886
2025-06-19 15:49:27,220 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_status.py first seen with mtime **********.3828883
2025-06-19 15:49:27,221 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_namespace_utils.py first seen with mtime **********.8494825
2025-06-19 15:49:27,221 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\job_create_params.py first seen with mtime **********.3508933
2025-06-19 15:49:27,221 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_with_reference_param.py first seen with mtime **********.296202
2025-06-19 15:49:27,222 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\__init__.py first seen with mtime **********.67819
2025-06-19 15:49:27,222 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\admin\migrations\0003_logentry_add_action_flag_choices.py first seen with mtime **********.4056838
2025-06-19 15:49:27,222 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job_wandb_integration_object.py first seen with mtime **********.3508933
2025-06-19 15:49:27,223 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_function.py first seen with mtime **********.2832005
2025-06-19 15:49:27,223 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\function_tool.py first seen with mtime **********.355893
2025-06-19 15:49:27,223 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_truncated_event.py first seen with mtime **********.296202
2025-06-19 15:49:27,223 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_append_event.py first seen with mtime **********.2973366
2025-06-19 15:49:27,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\MySQLdb\connections.py first seen with mtime 1742637949.669361
2025-06-19 15:49:27,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\embedding_model.py first seen with mtime **********.261019
2025-06-19 15:49:27,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_output_item_added_event.py first seen with mtime **********.308454
2025-06-19 15:49:27,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\annotation.py first seen with mtime **********.3144498
2025-06-19 15:49:27,224 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\layers.py first seen with mtime **********.1065862
2025-06-19 15:49:27,225 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_format_text_config_param.py first seen with mtime **********.3678927
2025-06-19 15:49:27,225 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\function_parameters.py first seen with mtime **********.3968883
2025-06-19 15:49:27,226 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\MySQLdb\cursors.py first seen with mtime 1742637949.6703618
2025-06-19 15:49:27,226 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\parsed_function_tool_call.py first seen with mtime **********.3478932
2025-06-19 15:49:27,226 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread_update_params.py first seen with mtime **********.2882006
2025-06-19 15:49:27,226 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\type_adapter.py first seen with mtime **********.8394818
2025-06-19 15:49:27,227 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_create_event_param.py first seen with mtime **********.3064454
2025-06-19 15:49:27,227 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_testing.py first seen with mtime **********.68119
2025-06-19 15:49:27,227 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\parsed_chat_completion.py first seen with mtime **********.3478932
2025-06-19 15:49:27,228 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch_error.py first seen with mtime **********.2570195
2025-06-19 15:49:27,228 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_annotation_delta_event.py first seen with mtime **********.3838882
2025-06-19 15:49:27,228 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\__init__.py first seen with mtime **********.2210486
2025-06-19 15:49:27,229 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_call_in_progress_event.py first seen with mtime **********.3598928
2025-06-19 15:49:27,229 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_tool_call_param.py first seen with mtime **********.370893
2025-06-19 15:49:27,230 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_base_client.py first seen with mtime **********.204862
2025-06-19 15:49:27,230 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread_create_params.py first seen with mtime **********.2882006
2025-06-19 15:49:27,230 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_item_param.py first seen with mtime **********.374893
2025-06-19 15:49:27,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\file_search_tool_call_delta.py first seen with mtime **********.330889
2025-06-19 15:49:27,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\file_proxy.py first seen with mtime **********.852939
2025-06-19 15:49:27,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_create_params.py first seen with mtime **********.4018884
2025-06-19 15:49:27,231 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\styles\_mapping.py first seen with mtime **********.9168081
2025-06-19 15:49:27,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\env_settings.py first seen with mtime **********.8614824
2025-06-19 15:49:27,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\file_citation_annotation.py first seen with mtime **********.3154497
2025-06-19 15:49:27,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store_create_params.py first seen with mtime **********.2712011
2025-06-19 15:49:27,232 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\__init__.py first seen with mtime **********.2299788
2025-06-19 15:49:27,233 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\zstandard\backend_c.cp311-win_amd64.pyd first seen with mtime 1742633606.8127646
2025-06-19 15:49:27,233 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\file_search_tool_param.py first seen with mtime **********.355893
2025-06-19 15:49:27,233 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\gpt_CWSC\asgi.py first seen with mtime **********.0
2025-06-19 15:49:27,233 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\abc.py first seen with mtime **********.0
2025-06-19 15:49:27,234 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio_response_format.py first seen with mtime **********.2560194
2025-06-19 15:49:27,234 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_content_part_added_event.py first seen with mtime **********.3054254
2025-06-19 15:49:27,234 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_image_url_input_param.py first seen with mtime **********.2672012
2025-06-19 15:49:27,235 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\segment.py first seen with mtime **********.8639438
2025-06-19 15:49:27,235 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\__init__.py first seen with mtime **********.2129188
2025-06-19 15:49:27,235 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_commit_event.py first seen with mtime **********.298427
2025-06-19 15:49:27,235 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_developer_message_param.py first seen with mtime **********.3398929
2025-06-19 15:49:27,235 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\uploads\__init__.py first seen with mtime **********.3988883
2025-06-19 15:49:27,236 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_compat.py first seen with mtime **********.2049081
2025-06-19 15:49:27,236 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch_create_params.py first seen with mtime **********.2570195
2025-06-19 15:49:27,236 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_word.py first seen with mtime **********.2782006
2025-06-19 15:49:27,236 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_purpose.py first seen with mtime **********.2630188
2025-06-19 15:49:27,237 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_content_param.py first seen with mtime **********.2902007
2025-06-19 15:49:27,237 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_forward_ref.py first seen with mtime **********.8464828
2025-06-19 15:49:27,237 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_create_event_param.py first seen with mtime **********.291201
2025-06-19 15:49:27,238 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\chat\_types.py first seen with mtime **********.232977
2025-06-19 15:49:27,238 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step.py first seen with mtime **********.331889
2025-06-19 15:49:27,238 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\plugin\__init__.py first seen with mtime **********.8564825
2025-06-19 15:49:27,238 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\chat\__init__.py first seen with mtime **********.2309766
2025-06-19 15:49:27,239 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\__init__.py first seen with mtime **********.5942636
2025-06-19 15:49:27,239 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_param.py first seen with mtime **********.2942014
2025-06-19 15:49:27,239 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_content_part_done_event.py first seen with mtime **********.3638935
2025-06-19 15:49:27,239 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\response_format_text.py first seen with mtime **********.3938882
2025-06-19 15:49:27,240 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_speech_started_event.py first seen with mtime **********.2994251
2025-06-19 15:49:27,240 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_list_params.py first seen with mtime **********.262019
2025-06-19 15:49:27,240 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\db.py first seen with mtime **********.1055863
2025-06-19 15:49:27,240 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_web_search.py first seen with mtime **********.370893
2025-06-19 15:49:27,240 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\pagination.py first seen with mtime **********.210919
2025-06-19 15:49:27,241 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_sockets.py first seen with mtime **********.6791902
2025-06-19 15:49:27,241 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\tool_call_delta.py first seen with mtime **********.334893
2025-06-19 15:49:27,241 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_migration.py first seen with mtime **********.8303034
2025-06-19 15:49:27,241 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\from_thread.py first seen with mtime **********.669136
2025-06-19 15:49:27,242 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_deleted.py first seen with mtime **********.2802007
2025-06-19 15:49:27,242 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\__init__.py first seen with mtime **********.6731365
2025-06-19 15:49:27,242 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_audio_done_event.py first seen with mtime **********.3044255
2025-06-19 15:49:27,242 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\auth.py first seen with mtime **********.1045432
2025-06-19 15:49:27,243 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\progress.py first seen with mtime **********.8599439
2025-06-19 15:49:27,243 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\util.py first seen with mtime **********.8118074
2025-06-19 15:49:27,243 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_utils.py first seen with mtime **********.2159152
2025-06-19 15:49:27,243 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\runs\__init__.py first seen with mtime **********.2450187
2025-06-19 15:49:27,243 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_created_event.py first seen with mtime **********.3074307
2025-06-19 15:49:27,244 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_function_call_arguments_delta_event.py first seen with mtime **********.3074307
2025-06-19 15:49:27,244 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_legacy_response.py first seen with mtime **********.2069192
2025-06-19 15:49:27,245 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_tool_call.py first seen with mtime **********.3668928
2025-06-19 15:49:27,245 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\protocol.py first seen with mtime **********.860944
2025-06-19 15:49:27,246 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_named_tool_choice_param.py first seen with mtime **********.341893
2025-06-19 15:49:27,246 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_refusal_done_event.py first seen with mtime **********.3818884
2025-06-19 15:49:27,246 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_status_codes.py first seen with mtime **********.124564
2025-06-19 15:49:27,247 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\__init__.py first seen with mtime **********.3358927
2025-06-19 15:49:27,247 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_audio_transcript_delta_event.py first seen with mtime **********.3588927
2025-06-19 15:49:27,247 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\style.py first seen with mtime **********.8098085
2025-06-19 15:49:27,248 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_utils.py first seen with mtime **********.5962598
2025-06-19 15:49:27,248 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_null_file.py first seen with mtime **********.843939
2025-06-19 15:49:27,248 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_code_interpreter_tool_call.py first seen with mtime **********.3608942
2025-06-19 15:49:27,249 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_message_content_list.py first seen with mtime **********.374893
2025-06-19 15:49:27,249 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\responses_model.py first seen with mtime **********.3948882
2025-06-19 15:49:27,249 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_log_render.py first seen with mtime **********.843939
2025-06-19 15:49:27,250 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\color.py first seen with mtime **********.8594823
2025-06-19 15:49:27,250 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\markup.py first seen with mtime **********.8569446
2025-06-19 15:49:27,250 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_stream_event.py first seen with mtime **********.2822006
2025-06-19 15:49:27,250 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\job_list_events_params.py first seen with mtime **********.3518999
2025-06-19 15:49:27,251 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_testing.py first seen with mtime **********.67819
2025-06-19 15:49:27,251 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_client.py first seen with mtime **********.121479
2025-06-19 15:49:27,251 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\consumers.py first seen with mtime 1750316675.2093546
2025-06-19 15:49:27,252 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\__init__.py first seen with mtime **********.5972836
2025-06-19 15:49:27,252 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\main.py first seen with mtime **********.8644824
2025-06-19 15:49:27,253 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\__init__.py first seen with mtime **********.24902
2025-06-19 15:49:27,253 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text_delta.py first seen with mtime **********.3274493
2025-06-19 15:49:27,253 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\file_search_tool.py first seen with mtime **********.3548927
2025-06-19 15:49:27,254 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_refusal_param.py first seen with mtime **********.3798888
2025-06-19 15:49:27,254 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\file_citation_delta_annotation.py first seen with mtime **********.3154497
2025-06-19 15:49:27,254 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_delete_event.py first seen with mtime **********.2922018
2025-06-19 15:49:27,254 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_content.py first seen with mtime **********.3204496
2025-06-19 15:49:27,255 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\vector_stores\__init__.py first seen with mtime **********.2530196
2025-06-19 15:49:27,255 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant.py first seen with mtime **********.2802007
2025-06-19 15:49:27,256 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\interfaces.py first seen with mtime **********.6053448
2025-06-19 15:49:27,256 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\images_response.py first seen with mtime **********.265019
2025-06-19 15:49:27,257 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\style.py first seen with mtime **********.864944
2025-06-19 15:49:27,257 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\syntax.py first seen with mtime **********.8659437
2025-06-19 15:49:27,257 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_import_utils.py first seen with mtime **********.8484826
2025-06-19 15:49:27,258 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\transcriptions.py first seen with mtime **********.2390192
2025-06-19 15:49:27,258 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\views\defaults.py first seen with mtime **********.7457466
2025-06-19 15:49:27,258 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_content.py first seen with mtime **********.2902007
2025-06-19 15:49:27,258 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_generics.py first seen with mtime **********.8474824
2025-06-19 15:49:27,258 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\migrations\0003_initial.py first seen with mtime 1733578445.0
2025-06-19 15:49:27,259 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_error_event.py first seen with mtime **********.364893
2025-06-19 15:49:27,259 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image_edit_params.py first seen with mtime **********.264019
2025-06-19 15:49:27,259 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\file_path_annotation.py first seen with mtime **********.3164494
2025-06-19 15:49:27,259 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\default.py first seen with mtime **********.1275637
2025-06-19 15:49:27,259 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_functools.py first seen with mtime **********.0
2025-06-19 15:49:27,260 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_token_logprob.py first seen with mtime **********.344893
2025-06-19 15:49:27,260 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0002_delete_userinfo.py first seen with mtime **********.0
2025-06-19 15:49:27,260 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_submit_tool_outputs_params.py first seen with mtime **********.3254492
2025-06-19 15:49:27,261 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_create_response.py first seen with mtime **********.310366
2025-06-19 15:49:27,261 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_content_block.py first seen with mtime **********.317449
2025-06-19 15:49:27,262 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_file.py first seen with mtime **********.3728926
2025-06-19 15:49:27,262 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_create_params.py first seen with mtime **********.310366
2025-06-19 15:49:27,262 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_util.py first seen with mtime **********.2240448
2025-06-19 15:49:27,262 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\upload_complete_params.py first seen with mtime **********.2702012
2025-06-19 15:49:27,263 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_text.py first seen with mtime **********.0
2025-06-19 15:49:27,263 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\upload.py first seen with mtime **********.2702012
2025-06-19 15:49:27,263 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_tools.py first seen with mtime **********.2269764
2025-06-19 15:49:27,263 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\chat_model.py first seen with mtime **********.3898883
2025-06-19 15:49:27,264 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_stream_options_param.py first seen with mtime **********.343893
2025-06-19 15:49:27,264 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_sockets.py first seen with mtime **********.6751904
2025-06-19 15:49:27,264 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_create_params.py first seen with mtime **********.262019
2025-06-19 15:49:27,265 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_content_part_param.py first seen with mtime **********.3214495
2025-06-19 15:49:27,265 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\streams\stapled.py first seen with mtime **********.6821902
2025-06-19 15:49:27,265 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_backends\__init__.py first seen with mtime **********.6003442
2025-06-19 15:49:27,266 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0004_rename_token_num_usage_output_token_and_more.py first seen with mtime **********.0
2025-06-19 15:49:27,266 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_content_param.py first seen with mtime **********.3728926
2025-06-19 15:49:27,266 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_refusal_delta_event.py first seen with mtime **********.3818884
2025-06-19 15:49:27,267 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call_param.py first seen with mtime **********.362893
2025-06-19 15:49:27,267 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_model.py first seen with mtime **********.2672012
2025-06-19 15:49:27,267 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_auth.py first seen with mtime **********.120479
2025-06-19 15:49:27,267 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\__init__.py first seen with mtime **********.8584824
2025-06-19 15:49:27,268 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\__init__.py first seen with mtime **********.7053769
2025-06-19 15:49:27,268 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\version.py first seen with mtime **********.8424823
2025-06-19 15:49:27,268 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_list_params.py first seen with mtime **********.3254492
2025-06-19 15:49:27,268 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_function_message_param.py first seen with mtime **********.3398929
2025-06-19 15:49:27,269 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\streams\__init__.py first seen with mtime **********.68119
2025-06-19 15:49:27,269 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\errors.py first seen with mtime **********.852939
2025-06-19 15:49:27,269 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_image_param.py first seen with mtime **********.3378928
2025-06-19 15:49:27,270 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_text_done_event.py first seen with mtime **********.2772007
2025-06-19 15:49:27,270 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\filter.py first seen with mtime **********.8078082
2025-06-19 15:49:27,270 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_chunking_strategy.py first seen with mtime **********.261019
2025-06-19 15:49:27,271 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_truncate_event_param.py first seen with mtime **********.295267
2025-06-19 15:49:27,271 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\theme.py first seen with mtime **********.8679438
2025-06-19 15:49:27,271 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_create_event.py first seen with mtime **********.3064454
2025-06-19 15:49:27,271 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\certifi\__init__.py first seen with mtime **********.4121935
2025-06-19 15:49:27,272 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\reasoning_effort.py first seen with mtime **********.3928883
2025-06-19 15:49:27,272 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_cancel_event.py first seen with mtime **********.3054254
2025-06-19 15:49:27,272 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\token.py first seen with mtime **********.8108082
2025-06-19 15:49:27,272 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_parsing\__init__.py first seen with mtime **********.2289767
2025-06-19 15:49:27,273 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\files.py first seen with mtime **********.236011
2025-06-19 15:49:27,273 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0001_initial.py first seen with mtime **********.701161
2025-06-19 15:49:27,273 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\migrations\__init__.py first seen with mtime 1733578445.0
2025-06-19 15:49:27,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\assistants.py first seen with mtime **********.2400205
2025-06-19 15:49:27,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_audio.py first seen with mtime **********.336893
2025-06-19 15:49:27,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\other_file_chunking_strategy_object.py first seen with mtime **********.2682009
2025-06-19 15:49:27,274 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\migrations\executor.py first seen with mtime **********.6474533
2025-06-19 15:49:27,275 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_models.py first seen with mtime **********.5952597
2025-06-19 15:49:27,275 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_update_event.py first seen with mtime **********.3113666
2025-06-19 15:49:27,276 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\wsgi.py first seen with mtime **********.1285636
2025-06-19 15:49:27,276 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\http11.py first seen with mtime **********.6043444
2025-06-19 15:49:27,277 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\generics.py first seen with mtime **********.8354826
2025-06-19 15:49:27,277 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\annotation_delta.py first seen with mtime **********.3154497
2025-06-19 15:49:27,277 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\static_file_chunking_strategy.py first seen with mtime **********.2682009
2025-06-19 15:49:27,278 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_store_message.py first seen with mtime **********.3428926
2025-06-19 15:49:27,278 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\migrations\0001_initial.py first seen with mtime 1733578445.0
2025-06-19 15:49:27,278 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0005_alter_user_last_login_null.py first seen with mtime **********.7021997
2025-06-19 15:49:27,278 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\models\sql\compiler.py first seen with mtime **********.6684542
2025-06-19 15:49:27,279 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\models.py first seen with mtime **********.2370188
2025-06-19 15:49:27,279 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\responses\__init__.py first seen with mtime **********.2510195
2025-06-19 15:49:27,280 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session_update_param.py first seen with mtime **********.3134074
2025-06-19 15:49:27,280 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\code_interpreter_output_image.py first seen with mtime **********.3294454
2025-06-19 15:49:27,281 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_validators.py first seen with mtime **********.8524857
2025-06-19 15:49:27,281 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\decorator.py first seen with mtime **********.8614824
2025-06-19 15:49:27,281 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\response_format_json_object.py first seen with mtime **********.3978882
2025-06-19 15:49:27,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\chat\chat.py first seen with mtime **********.2420187
2025-06-19 15:49:27,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\fields.py first seen with mtime **********.8634825
2025-06-19 15:49:27,282 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\code_interpreter_logs.py first seen with mtime **********.3284492
2025-06-19 15:49:27,283 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\pretty.py first seen with mtime **********.8589437
2025-06-19 15:49:27,283 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\base\migrations\__init__.py first seen with mtime 1733578441.0
2025-06-19 15:49:27,284 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\completion.py first seen with mtime **********.2580192
2025-06-19 15:49:27,284 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_constants.py first seen with mtime **********.2059197
2025-06-19 15:49:27,284 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_update_params.py first seen with mtime **********.3264492
2025-06-19 15:49:27,284 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_main.py first seen with mtime **********.1235194
2025-06-19 15:49:27,285 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\uploads\uploads.py first seen with mtime **********.2530196
2025-06-19 15:49:27,285 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\generic\websocket.py first seen with mtime **********.109586
2025-06-19 15:49:27,286 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\code_interpreter_tool.py first seen with mtime **********.2852006
2025-06-19 15:49:27,287 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_mock_val_ser.py first seen with mtime **********.8494825
2025-06-19 15:49:27,287 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\response_format_json_schema.py first seen with mtime **********.3978882
2025-06-19 15:49:27,287 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_config.py first seen with mtime **********.3838882
2025-06-19 15:49:27,288 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\jiter\__init__.py first seen with mtime **********.0609927
2025-06-19 15:49:27,288 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_types.py first seen with mtime **********.124564
2025-06-19 15:49:27,288 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_tool_choice_option_param.py first seen with mtime **********.344893
2025-06-19 15:49:27,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\audio.py first seen with mtime **********.238019
2025-06-19 15:49:27,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_content_block_param.py first seen with mtime **********.3194492
2025-06-19 15:49:27,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\validators.py first seen with mtime **********.8684824
2025-06-19 15:49:27,289 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\__init__.py first seen with mtime **********.1194832
2025-06-19 15:49:27,290 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\function_tool.py first seen with mtime **********.286201
2025-06-19 15:49:27,292 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_receivebuffer.py first seen with mtime **********.2230449
2025-06-19 15:49:27,293 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_subprocesses.py first seen with mtime **********.6761901
2025-06-19 15:49:27,293 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_web_search_call_completed_event.py first seen with mtime **********.385888
2025-06-19 15:49:27,294 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_async\connection.py first seen with mtime **********.5972836
2025-06-19 15:49:27,294 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_message_param.py first seen with mtime **********.3408928
2025-06-19 15:49:27,294 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_content_part_param.py first seen with mtime **********.3378928
2025-06-19 15:49:27,295 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_tasks.py first seen with mtime **********.6771903
2025-06-19 15:49:27,296 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_parsing\_responses.py first seen with mtime **********.2299788
2025-06-19 15:49:27,297 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch.py first seen with mtime **********.2560194
2025-06-19 15:49:27,298 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_input_audio_transcription_delta_event.py first seen with mtime **********.293202
2025-06-19 15:49:27,298 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_tempfile.py first seen with mtime **********.6771903
2025-06-19 15:49:27,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0009_alter_user_last_name_max_length.py first seen with mtime **********.7043734
2025-06-19 15:49:27,299 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\metadata.py first seen with mtime **********.3968883
2025-06-19 15:49:27,300 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_content.py first seen with mtime **********.3728926
2025-06-19 15:49:27,300 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\regex\regex.py first seen with mtime **********.8971746
2025-06-19 15:49:27,300 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_clear_event_param.py first seen with mtime **********.298427
2025-06-19 15:49:27,301 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\filecontrol\migrations\0003_alter_fileinfo_create_time_alter_fileinfo_size.py first seen with mtime 1733578442.0
2025-06-19 15:49:27,301 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\regex\_regex_core.py first seen with mtime **********.8971746
2025-06-19 15:49:27,302 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\_tiktoken.cp311-win_amd64.pyd first seen with mtime **********.9795818
2025-06-19 15:49:27,304 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_includable.py first seen with mtime **********.3718927
2025-06-19 15:49:27,304 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_utils.py first seen with mtime **********.1255639
2025-06-19 15:49:27,306 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_ratio.py first seen with mtime **********.844939
2025-06-19 15:49:27,307 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\screen.py first seen with mtime **********.8639438
2025-06-19 15:49:27,307 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0003_initial.py first seen with mtime **********.0
2025-06-19 15:49:27,307 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_input_audio_transcription_completed_event.py first seen with mtime **********.293202
2025-06-19 15:49:27,308 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completion_update_params.py first seen with mtime **********.346893
2025-06-19 15:49:27,308 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\measure.py first seen with mtime **********.8579438
2025-06-19 15:49:27,308 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\main.py first seen with mtime **********.8374825
2025-06-19 15:49:27,310 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\file_search_tool_param.py first seen with mtime **********.286201
2025-06-19 15:49:27,310 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\transcription_session_update.py first seen with mtime **********.3134074
2025-06-19 15:49:27,311 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared_params\compound_filter.py first seen with mtime **********.395888
2025-06-19 15:49:27,312 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_version.py first seen with mtime **********.2240448
2025-06-19 15:49:27,312 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\completion_usage.py first seen with mtime **********.259019
2025-06-19 15:49:27,312 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_create_response.py first seen with mtime **********.2752008
2025-06-19 15:49:27,313 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\chat.py first seen with mtime **********.2470188
2025-06-19 15:49:27,313 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_writers.py first seen with mtime **********.2240448
2025-06-19 15:49:27,314 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_delta_block.py first seen with mtime **********.3184493
2025-06-19 15:49:27,314 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item.py first seen with mtime **********.2902007
2025-06-19 15:49:27,315 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_call_arguments_done_event.py first seen with mtime **********.368893
2025-06-19 15:49:27,315 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_delta_block.py first seen with mtime **********.3194492
2025-06-19 15:49:27,315 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_failed_event.py first seen with mtime **********.365893
2025-06-19 15:49:27,316 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\utils.py first seen with mtime **********.108586
2025-06-19 15:49:27,316 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\abc.py first seen with mtime **********.0
2025-06-19 15:49:27,316 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_pick.py first seen with mtime **********.844939
2025-06-19 15:49:27,316 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job_event.py first seen with mtime **********.3498929
2025-06-19 15:49:27,317 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_synchronization.py first seen with mtime **********.5962598
2025-06-19 15:49:27,317 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\static_file_chunking_strategy_object.py first seen with mtime **********.2692013
2025-06-19 15:49:27,317 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_adapters.py first seen with mtime **********.0
2025-06-19 15:49:27,318 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_param.py first seen with mtime **********.2842007
2025-06-19 15:49:27,318 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_prediction_content_param.py first seen with mtime **********.3428926
2025-06-19 15:49:27,318 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_update_params.py first seen with mtime **********.2852006
2025-06-19 15:49:27,319 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:49:27,319 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_text_delta_event.py first seen with mtime **********.309369
2025-06-19 15:49:27,319 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:49:27,320 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\dataclasses.py first seen with mtime **********.8604825
2025-06-19 15:49:27,320 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\version.py first seen with mtime **********.8684824
2025-06-19 15:49:27,320 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:49:27.309110' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:49:27.309110', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:49:27,320 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_create_params.py first seen with mtime **********.3244493
2025-06-19 15:49:27,323 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0012_alter_user_first_name_max_length.py first seen with mtime **********.7053769
2025-06-19 15:49:27,323 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\jobs\__init__.py first seen with mtime **********.3528886
2025-06-19 15:49:27,324 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_stream_event.py first seen with mtime **********.3828883
2025-06-19 15:49:27,324 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:49:27,325 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_eventloop.py first seen with mtime **********.6731365
2025-06-19 15:49:27,326 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_exceptions.py first seen with mtime **********.5952597
2025-06-19 15:49:27,326 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio_model.py first seen with mtime **********.2550194
2025-06-19 15:49:27,327 - django.channels.server - INFO - HTTP GET / 200 [0.04, 127.0.0.1:58750]
2025-06-19 15:49:27,327 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message.py first seen with mtime **********.3204496
2025-06-19 15:49:27,328 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_config.py first seen with mtime **********.8434825
2025-06-19 15:49:27,329 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_core_metadata.py first seen with mtime **********.8434825
2025-06-19 15:49:27,329 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\responses\_types.py first seen with mtime **********.2339766
2025-06-19 15:49:27,329 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_fields.py first seen with mtime **********.8464828
2025-06-19 15:49:27,330 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\__init__.py first seen with mtime **********.3144498
2025-06-19 15:49:27,330 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_updated_event.py first seen with mtime **********.3123617
2025-06-19 15:49:27,330 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\responses\__init__.py first seen with mtime **********.232977
2025-06-19 15:49:27,331 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_text_done_event.py first seen with mtime **********.3848884
2025-06-19 15:49:27,331 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\colorsys.py first seen with mtime **********.0
2025-06-19 15:49:27,332 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_create_event.py first seen with mtime **********.291201
2025-06-19 15:49:27,332 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_exceptions.py first seen with mtime **********.6741648
2025-06-19 15:49:27,332 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_urlparse.py first seen with mtime **********.124564
2025-06-19 15:49:27,332 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_multi_modal_input_param.py first seen with mtime **********.2672012
2025-06-19 15:49:27,333 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\admin\migrations\__init__.py first seen with mtime **********.4066837
2025-06-19 15:49:27,333 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\http_proxy.py first seen with mtime **********.6043444
2025-06-19 15:49:27,333 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_content_response.py first seen with mtime **********.4008884
2025-06-19 15:49:27,334 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_events.py first seen with mtime **********.2220497
2025-06-19 15:49:27,334 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\live.py first seen with mtime **********.8559444
2025-06-19 15:49:27,334 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\migrations\__init__.py first seen with mtime **********.4805906
2025-06-19 15:49:27,335 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_chunking_strategy_param.py first seen with mtime **********.261019
2025-06-19 15:49:27,335 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\_pydantic.py first seen with mtime **********.2269764
2025-06-19 15:49:27,335 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_error.py first seen with mtime **********.364893
2025-06-19 15:49:27,336 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\__init__.py first seen with mtime **********.0
2025-06-19 15:49:27,336 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\threads.py first seen with mtime **********.2450187
2025-06-19 15:49:27,337 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\create_embedding_response.py first seen with mtime **********.2600193
2025-06-19 15:49:27,337 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\_common.py first seen with mtime **********.0
2025-06-19 15:49:27,337 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\__init__.py first seen with mtime **********.829813
2025-06-19 15:49:27,338 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_image.py first seen with mtime **********.373893
2025-06-19 15:49:27,338 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\resources\__init__.py first seen with mtime **********.0
2025-06-19 15:49:27,338 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_retrieve_event_param.py first seen with mtime **********.2942014
2025-06-19 15:49:27,339 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\box.py first seen with mtime **********.848939
2025-06-19 15:49:27,339 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call_output_item.py first seen with mtime **********.3618937
2025-06-19 15:49:27,339 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_item.py first seen with mtime **********.3768888
2025-06-19 15:49:27,340 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\computer_tool.py first seen with mtime **********.353893
2025-06-19 15:49:27,340 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\web_search_tool.py first seen with mtime **********.3888881
2025-06-19 15:49:27,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step_include.py first seen with mtime **********.3338926
2025-06-19 15:49:27,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\migrations\0001_initial.py first seen with mtime **********.479591
2025-06-19 15:49:27,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_client_event_param.py first seen with mtime **********.3014345
2025-06-19 15:49:27,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\function_tool_param.py first seen with mtime **********.355893
2025-06-19 15:49:27,341 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_item_list.py first seen with mtime **********.377889
2025-06-19 15:49:27,342 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\accounts\migrations\0001_initial.py first seen with mtime **********.0
2025-06-19 15:49:27,342 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\runs\steps.py first seen with mtime **********.246019
2025-06-19 15:49:27,342 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_deleted.py first seen with mtime **********.3214495
2025-06-19 15:49:27,343 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\auto_file_chunking_strategy_param.py first seen with mtime **********.2560194
2025-06-19 15:49:27,343 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\types.py first seen with mtime **********.8664825
2025-06-19 15:49:27,344 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_audio_transcript_done_event.py first seen with mtime **********.3044255
2025-06-19 15:49:27,344 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_generate_schema.py first seen with mtime **********.8474824
2025-06-19 15:49:27,345 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_tool_message_param.py first seen with mtime **********.344893
2025-06-19 15:49:27,345 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\file_path_delta_annotation.py first seen with mtime **********.3164494
2025-06-19 15:49:27,345 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\config.py first seen with mtime **********.8594823
2025-06-19 15:49:27,346 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\middleware.py first seen with mtime **********.1434848
2025-06-19 15:49:27,346 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic_core\_pydantic_core.cp311-win_amd64.pyd first seen with mtime **********.5570047
2025-06-19 15:49:27,346 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_deleted_event.py first seen with mtime **********.2922018
2025-06-19 15:49:27,346 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\containers.py first seen with mtime **********.8509388
2025-06-19 15:49:27,347 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\abc\_streams.py first seen with mtime **********.6801903
2025-06-19 15:49:27,347 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_tool_call.py first seen with mtime **********.369893
2025-06-19 15:49:27,347 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\session_created_event.py first seen with mtime **********.3113666
2025-06-19 15:49:27,348 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\refusal_delta_block.py first seen with mtime **********.3234491
2025-06-19 15:49:27,348 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_discriminated_union.py first seen with mtime **********.8454869
2025-06-19 15:49:27,349 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\moderation_text_input_param.py first seen with mtime **********.2682009
2025-06-19 15:49:27,349 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\__init__.py first seen with mtime **********.3199975
2025-06-19 15:49:27,350 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\thread_deleted.py first seen with mtime **********.2882006
2025-06-19 15:49:27,350 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_models.py first seen with mtime **********.1235194
2025-06-19 15:49:27,350 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\filecontrol\migrations\__init__.py first seen with mtime 1733578442.0
2025-06-19 15:49:27,350 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_model_construction.py first seen with mtime **********.8494825
2025-06-19 15:49:27,351 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\model.py first seen with mtime **********.265019
2025-06-19 15:49:27,351 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\color_triplet.py first seen with mtime **********.8499389
2025-06-19 15:49:27,351 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\uploads\__init__.py first seen with mtime **********.2520196
2025-06-19 15:49:27,351 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_incomplete_event.py first seen with mtime **********.3718927
2025-06-19 15:49:27,352 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\static_file_chunking_strategy_param.py first seen with mtime **********.2692013
2025-06-19 15:49:27,352 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\uploads\upload_part.py first seen with mtime **********.3998883
2025-06-19 15:49:27,352 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\version.py first seen with mtime **********.210919
2025-06-19 15:49:27,352 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\uploads\part_create_params.py first seen with mtime **********.3998883
2025-06-19 15:49:27,353 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\streaming\responses\_responses.py first seen with mtime **********.2339766
2025-06-19 15:49:27,353 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\transcription_create_params.py first seen with mtime **********.2752008
2025-06-19 15:49:27,353 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text.py first seen with mtime **********.3264492
2025-06-19 15:49:27,354 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\threads\__init__.py first seen with mtime **********.2440188
2025-06-19 15:49:27,354 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\fields.py first seen with mtime **********.8344827
2025-06-19 15:49:27,354 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_store.py first seen with mtime **********.2712011
2025-06-19 15:49:27,354 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completion_create_params.py first seen with mtime **********.346893
2025-06-19 15:49:27,355 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job_integration.py first seen with mtime **********.3498929
2025-06-19 15:49:27,355 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\tool_choice_options.py first seen with mtime **********.3868883
2025-06-19 15:49:27,355 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call_output_screenshot.py first seen with mtime **********.3618937
2025-06-19 15:49:27,356 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\filecontrol\migrations\0001_initial.py first seen with mtime 1733578442.0
2025-06-19 15:49:27,356 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_ssl.py first seen with mtime **********.5952597
2025-06-19 15:49:27,356 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response.py first seen with mtime **********.3578937
2025-06-19 15:49:27,356 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_call_in_progress_event.py first seen with mtime **********.365893
2025-06-19 15:49:27,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\vector_store_file.py first seen with mtime **********.4028885
2025-06-19 15:49:27,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_audio_param.py first seen with mtime **********.336893
2025-06-19 15:49:27,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\views.py first seen with mtime **********.5822737
2025-06-19 15:49:27,357 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_input_image_param.py first seen with mtime **********.373893
2025-06-19 15:49:27,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_web_search_param.py first seen with mtime **********.370893
2025-06-19 15:49:27,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\exceptions.py first seen with mtime **********.1055863
2025-06-19 15:49:27,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\file_batch_list_files_params.py first seen with mtime **********.4008884
2025-06-19 15:49:27,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\ansi.py first seen with mtime **********.847939
2025-06-19 15:49:27,358 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\exceptions.py first seen with mtime **********.317997
2025-06-19 15:49:27,359 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\run_step_delta_event.py first seen with mtime **********.3328934
2025-06-19 15:49:27,359 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\__init__.py first seen with mtime **********.1265635
2025-06-19 15:49:27,359 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\realtime_response.py first seen with mtime **********.3024244
2025-06-19 15:49:27,360 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\contenttypes\migrations\0002_remove_content_type_name.py first seen with mtime **********.8052022
2025-06-19 15:49:27,360 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_internal_dataclass.py first seen with mtime **********.8484826
2025-06-19 15:49:27,360 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_streams.py first seen with mtime **********.2149189
2025-06-19 15:49:27,361 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_output_item_added_event.py first seen with mtime **********.377889
2025-06-19 15:49:27,361 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_retrieve_params.py first seen with mtime **********.3818884
2025-06-19 15:49:27,361 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_commit_event_param.py first seen with mtime **********.2994251
2025-06-19 15:49:27,361 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_client.py first seen with mtime **********.2049081
2025-06-19 15:49:27,362 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_create_params.py first seen with mtime **********.3214495
2025-06-19 15:49:27,362 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\reasoning.py first seen with mtime **********.3928883
2025-06-19 15:49:27,362 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\translation.py first seen with mtime **********.2782006
2025-06-19 15:49:27,362 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\sniffio\_impl.py first seen with mtime 1742633606.9900455
2025-06-19 15:49:27,363 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\image_model.py first seen with mtime **********.264019
2025-06-19 15:49:27,363 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\error_wrappers.py first seen with mtime **********.8624823
2025-06-19 15:49:27,363 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\spinner.py first seen with mtime **********.8639438
2025-06-19 15:49:27,363 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\__init__.py first seen with mtime **********.2792006
2025-06-19 15:49:27,364 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\message_delta_event.py first seen with mtime **********.3224494
2025-06-19 15:49:27,364 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\sniffio\__init__.py first seen with mtime 1742633606.9900455
2025-06-19 15:49:27,364 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\middleware\common.py first seen with mtime **********.7097118
2025-06-19 15:49:27,365 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\fine_tuning.py first seen with mtime **********.24902
2025-06-19 15:49:27,365 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\file_object.py first seen with mtime **********.2630188
2025-06-19 15:49:27,365 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\input_audio_buffer_cleared_event.py first seen with mtime **********.298427
2025-06-19 15:49:27,366 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\warnings.py first seen with mtime **********.8424823
2025-06-19 15:49:27,366 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\required_action_function_tool_call.py first seen with mtime **********.3244493
2025-06-19 15:49:27,366 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_loop.py first seen with mtime **********.843939
2025-06-19 15:49:27,367 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\padding.py first seen with mtime **********.8579438
2025-06-19 15:49:27,367 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\filesize.py first seen with mtime **********.853939
2025-06-19 15:49:27,368 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\conversation_item_retrieve_event.py first seen with mtime **********.2942014
2025-06-19 15:49:27,368 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\static_file_chunking_strategy_object_param.py first seen with mtime **********.2692013
2025-06-19 15:49:27,368 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_role.py first seen with mtime **********.3428926
2025-06-19 15:49:27,368 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\lib\azure.py first seen with mtime **********.2279763
2025-06-19 15:49:27,369 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\chat\completions\messages.py first seen with mtime **********.248019
2025-06-19 15:49:27,369 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\tools\count_token.py first seen with mtime 1733578445.0
2025-06-19 15:49:27,369 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\speech_model.py first seen with mtime **********.2742007
2025-06-19 15:49:27,369 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_streams.py first seen with mtime **********.6761901
2025-06-19 15:49:27,370 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\responses\input_items.py first seen with mtime **********.2510195
2025-06-19 15:49:27,370 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\tiktoken\__init__.py first seen with mtime **********.9695818
2025-06-19 15:49:27,370 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\realtime\transcription_sessions.py first seen with mtime **********.2440188
2025-06-19 15:49:27,370 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_transports\asgi.py first seen with mtime **********.1265635
2025-06-19 15:49:27,371 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\connection_pool.py first seen with mtime **********.6033444
2025-06-19 15:49:27,371 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\beta.py first seen with mtime **********.2410185
2025-06-19 15:49:27,371 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpcore\_sync\__init__.py first seen with mtime **********.6033444
2025-06-19 15:49:27,371 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\default_styles.py first seen with mtime **********.851939
2025-06-19 15:49:27,371 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\tools\update_content.py first seen with mtime 1733578445.0
2025-06-19 15:49:27,372 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_url_delta.py first seen with mtime **********.3194492
2025-06-19 15:49:27,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_logs.py first seen with mtime **********.2139187
2025-06-19 15:49:27,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_content_part_added_event.py first seen with mtime **********.362893
2025-06-19 15:49:27,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\streams\tls.py first seen with mtime **********.68319
2025-06-19 15:49:27,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\zstandard\__init__.py first seen with mtime 1742633606.8067648
2025-06-19 15:49:27,373 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_call_completed_event.py first seen with mtime **********.365893
2025-06-19 15:49:27,374 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\_extension.py first seen with mtime **********.842939
2025-06-19 15:49:27,374 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\errors.py first seen with mtime **********.8624823
2025-06-19 15:49:27,375 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\h11\_connection.py first seen with mtime **********.2210486
2025-06-19 15:49:27,375 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\contenttypes\migrations\__init__.py first seen with mtime **********.8052022
2025-06-19 15:49:27,376 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_typing_extra.py first seen with mtime **********.8514824
2025-06-19 15:49:27,376 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic_core\core_schema.py first seen with mtime **********.535289
2025-06-19 15:49:27,376 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\filters\__init__.py first seen with mtime **********.8118074
2025-06-19 15:49:27,376 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\streams\memory.py first seen with mtime **********.6821902
2025-06-19 15:49:27,376 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_param.py first seen with mtime **********.2842007
2025-06-19 15:49:27,377 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\fractions.py first seen with mtime **********.0
2025-06-19 15:49:27,377 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\vector_stores\files.py first seen with mtime **********.2540193
2025-06-19 15:49:27,377 - django.utils.autoreload - DEBUG - File C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\importlib\metadata\_collections.py first seen with mtime **********.0
2025-06-19 15:49:27,378 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\__init__.py first seen with mtime **********.8058078
2025-06-19 15:49:27,378 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_std_types_schema.py first seen with mtime **********.8514824
2025-06-19 15:49:27,379 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\_core\_fileio.py first seen with mtime **********.6741648
2025-06-19 15:49:27,379 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\fine_tuning\jobs\jobs.py first seen with mtime **********.2500196
2025-06-19 15:49:27,379 - django.utils.autoreload - DEBUG - File D:\硕士\aiagent\gpt_CWSC\style_content\migrations\0002_delete_usage.py first seen with mtime 1733578445.0
2025-06-19 15:49:27,379 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat_model.py first seen with mtime **********.2580192
2025-06-19 15:49:27,380 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\contenttypes\migrations\0001_initial.py first seen with mtime **********.8042026
2025-06-19 15:49:27,380 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_audio_done_event.py first seen with mtime **********.3578937
2025-06-19 15:49:27,381 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\images.py first seen with mtime **********.236011
2025-06-19 15:49:27,381 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\responses\responses.py first seen with mtime **********.2510195
2025-06-19 15:49:27,381 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_created_event.py first seen with mtime **********.364893
2025-06-19 15:49:27,382 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_function_call_arguments_delta_event.py first seen with mtime **********.368893
2025-06-19 15:49:27,382 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0011_update_proxy_permissions.py first seen with mtime **********.7043734
2025-06-19 15:49:27,383 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_computer_tool_call.py first seen with mtime **********.3618937
2025-06-19 15:49:27,383 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\run_status.py first seen with mtime **********.3254492
2025-06-19 15:49:27,383 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\text_delta_block.py first seen with mtime **********.3274493
2025-06-19 15:49:27,384 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\httpx\_exceptions.py first seen with mtime **********.122479
2025-06-19 15:49:27,384 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\backends.py first seen with mtime **********.5764039
2025-06-19 15:49:27,384 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\chat\completions.py first seen with mtime **********.2420187
2025-06-19 15:49:27,384 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\audio\translation_create_response.py first seen with mtime **********.2792006
2025-06-19 15:49:27,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_done_event.py first seen with mtime **********.3074307
2025-06-19 15:49:27,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_in_progress_event.py first seen with mtime **********.3718927
2025-06-19 15:49:27,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\consumer.py first seen with mtime **********.1045432
2025-06-19 15:49:27,385 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\runs\tool_call.py first seen with mtime **********.3338926
2025-06-19 15:49:27,386 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_exceptions.py first seen with mtime **********.2059197
2025-06-19 15:49:27,386 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\_common.py first seen with mtime **********.2119188
2025-06-19 15:49:27,386 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\shared\__init__.py first seen with mtime **********.3898883
2025-06-19 15:49:27,387 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_extras\__init__.py first seen with mtime **********.2119188
2025-06-19 15:49:27,387 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\completion_list_params.py first seen with mtime **********.346893
2025-06-19 15:49:27,387 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_file_search_call_searching_event.py first seen with mtime **********.3668928
2025-06-19 15:49:27,387 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\regex\__init__.py first seen with mtime **********.893174
2025-06-19 15:49:27,388 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\_utils\_transform.py first seen with mtime **********.2149189
2025-06-19 15:49:27,388 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic_core\__init__.py first seen with mtime **********.5373993
2025-06-19 15:49:27,388 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\styled.py first seen with mtime **********.8659437
2025-06-19 15:49:27,389 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\vector_stores\vector_store_file_batch.py first seen with mtime **********.4028885
2025-06-19 15:49:27,389 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\anyio\to_thread.py first seen with mtime **********.6711361
2025-06-19 15:49:27,389 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_reasoning_effort.py first seen with mtime **********.3428926
2025-06-19 15:49:27,390 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\threads\image_file_delta.py first seen with mtime **********.317449
2025-06-19 15:49:27,390 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\audio\__init__.py first seen with mtime **********.238019
2025-06-19 15:49:27,390 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\resources\beta\realtime\sessions.py first seen with mtime **********.2430186
2025-06-19 15:49:27,390 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\realtime\response_output_item_done_event.py first seen with mtime **********.308454
2025-06-19 15:49:27,391 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\model_deleted.py first seen with mtime **********.265019
2025-06-19 15:49:27,391 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\annotated_handlers.py first seen with mtime **********.8314824
2025-06-19 15:49:27,391 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\batch_request_counts.py first seen with mtime **********.2580192
2025-06-19 15:49:27,391 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\rich\abc.py first seen with mtime **********.847939
2025-06-19 15:49:27,392 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\migrations\0010_alter_group_name_max_length.py first seen with mtime **********.7043734
2025-06-19 15:49:27,392 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_function_call_option_param.py first seen with mtime **********.3398929
2025-06-19 15:49:27,392 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\fine_tuning\fine_tuning_job_wandb_integration.py first seen with mtime **********.3498929
2025-06-19 15:49:27,393 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\channels\sessions.py first seen with mtime **********.1075864
2025-06-19 15:49:27,393 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_chunk.py first seen with mtime **********.336893
2025-06-19 15:49:27,394 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\db\backends\mysql\compiler.py first seen with mtime **********.6318858
2025-06-19 15:49:27,395 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pygments\lexers\_mapping.py first seen with mtime **********.8238082
2025-06-19 15:49:27,395 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\types.py first seen with mtime **********.8404825
2025-06-19 15:49:27,396 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\_internal\_core_utils.py first seen with mtime **********.8434825
2025-06-19 15:49:27,396 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\config.py first seen with mtime **********.8324826
2025-06-19 15:49:27,396 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\responses\response_reasoning_item.py first seen with mtime **********.3808885
2025-06-19 15:49:27,397 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\chat\chat_completion_deleted.py first seen with mtime **********.338893
2025-06-19 15:49:27,397 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\openai\types\beta\assistant_tool_choice_function_param.py first seen with mtime **********.2832005
2025-06-19 15:49:27,398 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\pydantic\v1\tools.py first seen with mtime **********.8664825
2025-06-19 15:49:27,717 - django.channels.server - INFO - WebSocket HANDSHAKING /ws/gpt/ [127.0.0.1:58754]
2025-06-19 15:49:27,723 - django.channels.server - WARNING - HTTP GET /static/pic/Wilson_Blade_v9.png 404 [0.01, 127.0.0.1:58750]
2025-06-19 15:49:27,725 - django.db.backends - DEBUG - (0.000) 
                SELECT VERSION(),
                       @@sql_mode,
                       @@default_storage_engine,
                       @@sql_auto_is_null,
                       @@lower_case_table_names,
                       CONVERT_TZ('2001-01-01 01:00:00', 'UTC', 'UTC') IS NOT NULL
            ; args=None; alias=default
2025-06-19 15:49:27,726 - django.db.backends - DEBUG - (0.000) SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED; args=None; alias=default
2025-06-19 15:49:27,727 - django.db.backends - DEBUG - (0.000) SELECT `django_session`.`session_key`, `django_session`.`session_data`, `django_session`.`expire_date` FROM `django_session` WHERE (`django_session`.`expire_date` > '2025-06-19 07:49:27.717995' AND `django_session`.`session_key` = '2vpm4gabph34pi7euybdz1i873igm4ls') LIMIT 21; args=('2025-06-19 07:49:27.717995', '2vpm4gabph34pi7euybdz1i873igm4ls'); alias=default
2025-06-19 15:49:27,729 - django.db.backends - DEBUG - (0.000) SELECT `auth_user`.`id`, `auth_user`.`password`, `auth_user`.`last_login`, `auth_user`.`is_superuser`, `auth_user`.`username`, `auth_user`.`first_name`, `auth_user`.`last_name`, `auth_user`.`email`, `auth_user`.`is_staff`, `auth_user`.`is_active`, `auth_user`.`date_joined` FROM `auth_user` WHERE `auth_user`.`id` = 2 LIMIT 21; args=(2,); alias=default
2025-06-19 15:49:27,731 - django.channels.server - INFO - WebSocket CONNECT /ws/gpt/ [127.0.0.1:58754]
2025-06-19 15:49:28,899 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\fallback.py first seen with mtime **********.1464849
2025-06-19 15:49:28,902 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime **********.321
2025-06-19 15:49:28,911 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\auth\context_processors.py first seen with mtime **********.5782743
2025-06-19 15:49:28,918 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\template\context_processors.py first seen with mtime **********.7127118
2025-06-19 15:49:28,923 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\cookie.py first seen with mtime **********.1464849
2025-06-19 15:49:28,937 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\sessions\serializers.py first seen with mtime **********.3189974
2025-06-19 15:49:28,941 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\storage\session.py first seen with mtime **********.1464849
2025-06-19 15:49:28,950 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\staticfiles\storage.py first seen with mtime **********.5785098
2025-06-19 15:49:28,959 - django.utils.autoreload - DEBUG - File E:\viruals\gpt_CWSC\Lib\site-packages\django\contrib\messages\context_processors.py first seen with mtime **********.1434848
2025-06-19 15:49:31,230 - django.channels.server - INFO - HTTP GET /copywritting/ 200 [0.01, 127.0.0.1:58766]
