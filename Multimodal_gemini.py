import os
import asyncio
import aiohttp
import aiofiles
from PIL import Image
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
import io
import base64

from typing import List, Dict, Union, Any


class AsyncImageAnalyzer:
    """
    基于Google Gemini的异步图片分析器类
    支持本地图片和网络图片的智能分析，统一接口处理单张或多张图片
    """

    def __init__(self, env_path=None, model_name="gemini-2.0-flash", temperature=0, timeout=30, max_retries=2):
        """
        初始化异步图片分析器

        参数:
            env_path (str): .env文件路径，如果为None则从当前目录加载
            model_name (str): 使用的模型名称
            temperature (float): 模型温度参数
            timeout (int): 请求超时时间
            max_retries (int): 最大重试次数
        """
        # 加载环境变量
        if env_path:
            load_dotenv(env_path)
        else:
            load_dotenv()

        # 获取API密钥
        self.api_key = os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("未找到GOOGLE_API_KEY环境变量，请检查.env文件配置")

        # 初始化模型参数
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.max_retries = max_retries

        # 初始化模型
        self._initialize_model()

    def _initialize_model(self):
        """初始化Google Gemini模型"""
        try:
            self.llm = ChatOpenAI(
                model=self.model_name,
                base_url='https://xiaoai.plus/v1',
                temperature=self.temperature,
                max_tokens=None,
                timeout=self.timeout,
                max_retries=self.max_retries,
                api_key=self.api_key,
            )
            print(f"成功初始化模型: {self.model_name}")
        except Exception as e:
            raise Exception(f"初始化模型失败: {e}")

    async def _process_local_image(self, image_path: str) -> str:
        """
        异步处理本地图片，转换为base64 data URL

        参数:
            image_path (str): 本地图片路径

        返回:
            str: base64编码的data URL
        """
        try:
            # 异步读取本地图片文件
            async with aiofiles.open(image_path, 'rb') as f:
                image_data = await f.read()

            # 使用PIL处理图片
            image_pil = Image.open(io.BytesIO(image_data))
            print(f"成功加载本地图片: {image_path}")

            # 将PIL图片对象转换为base64编码的data URL
            buffered = io.BytesIO()

            # 根据图片格式保存
            image_format = "JPEG"
            if image_path.lower().endswith('.png'):
                image_format = "PNG"
            elif image_path.lower().endswith('.gif'):
                image_format = "GIF"

            image_pil.save(buffered, format=image_format)

            # 编码为base64字符串
            img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")

            # 创建data URL
            mime_type_map = {
                "JPEG": "image/jpeg",
                "PNG": "image/png",
                "GIF": "image/gif"
            }
            mime_type = mime_type_map.get(image_format, "image/jpeg")
            image_data_url = f"data:{mime_type};base64,{img_str}"

            print("本地图片已成功转换为base64 data URL")
            return image_data_url

        except FileNotFoundError:
            print(f"错误: 找不到本地图片文件 {image_path}")
            return None
        except Exception as e:
            print(f"处理本地图片时出错: {e}")
            return None

    async def _process_web_image(self, image_url: str) -> str:
        """
        异步处理网络图片URL，下载并转换为base64 data URL

        参数:
            image_url (str): 网络图片URL

        返回:
            str: base64编码的data URL
        """
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(image_url) as response:
                    if response.status == 200:
                        image_data = await response.read()

                        # 获取content-type来确定图片格式
                        content_type = response.headers.get('content-type', 'image/jpeg')

                        # 将图片数据转换为base64
                        img_str = base64.b64encode(image_data).decode("utf-8")
                        image_data_url = f"data:{content_type};base64,{img_str}"

                        print(f"成功下载并处理网络图片: {image_url}")

                        return image_data_url
                    else:
                        print(f"下载网络图片失败，HTTP状态码: {response.status}")
                        return None
        except asyncio.TimeoutError:
            print(f"下载网络图片超时: {image_url}")
            return None
        except Exception as e:
            print(f"处理网络图片时出错: {e}")
            return None

    async def _process_single_image(self, image_source: str, is_local: bool = True) -> str:
        """
        异步处理单张图片的内部方法

        参数:
            image_source (str): 图片来源，可以是本地路径或网络URL
            is_local (bool): True表示本地图片，False表示网络URL

        返回:
            str: 可用于LLM的图片URL
        """
        if is_local:
            return await self._process_local_image(image_source)
        else:
            return await self._process_web_image(image_source)

    # ==== 新增: 处理 base64 图片 ====
    async def _process_base64_image(self, base64_data_url: str) -> str:
        """
        异步处理 base64 编码的图片数据 URL

        参数:
            base64_data_url (str): 形如 data:image/xxx;base64,xxxx 的字符串

        返回:
            str | None: 验证通过后返回原始 data URL，否则返回 None
        """
        try:
            # 基本格式校验
            if not base64_data_url.startswith("data:image/") or ";base64," not in base64_data_url:
                print("错误: 无效的 base64 图片数据 URL 格式")
                return None

            header, encoded = base64_data_url.split(",", 1)
            mime_type = header.split(":", 1)[1].split(";")[0]  # image/png 等
            if not mime_type.startswith("image/"):
                print(f"错误: 不支持的 MIME 类型: {mime_type}")
                return None

            # 尝试解码，验证是否为有效图片
            try:
                image_bytes = base64.b64decode(encoded)
                img = Image.open(io.BytesIO(image_bytes))
                img.verify()  # PIL 验证图片完整性
            except Exception as e:
                print(f"base64 图片数据验证失败: {e}")
                return None

            return base64_data_url  # 验证通过，直接返回
        except Exception as e:
            print(f"处理 base64 图片时出错: {e}")
            return None

    async def analyze_base64_images(self, base64_images: List[str],
                                    prompt: str = "使用中文描述这些图片的内容") -> Dict[str, Any]:
        """
        异步分析多张 base64 编码的图片

        参数:
            base64_images (List[str]): base64 data URL 列表
            prompt (str): 分析提示词

        返回:
            dict: 结果字典，与 analyze_image 返回格式一致
        """
        try:
            print(f"准备分析 {len(base64_images)} 张 base64 图片")

            tasks = [self._process_base64_image(data) for data in base64_images]
            image_results = await asyncio.gather(*tasks, return_exceptions=True)

            image_urls = []
            for i, res in enumerate(image_results):
                if isinstance(res, Exception):
                    print(f"处理第 {i + 1} 张 base64 图片出现异常: {res}")
                elif res is not None:
                    image_urls.append(res)
                else:
                    print(f"跳过第 {i + 1} 张 base64 图片（处理失败）")

            if not image_urls:
                return {
                    "success": False,
                    "error": "所有 base64 图片处理失败",
                    "result": None,
                    "processed_images_count": 0,
                    "total_images_count": len(base64_images)
                }

            # 构建消息
            content = [{"type": "text", "text": prompt}] + [
                {"type": "image_url", "image_url": {"url": url}} for url in image_urls
            ]
            message = HumanMessage(content=content)
            response = await self.llm.ainvoke([message])

            return {
                "success": True,
                "error": None,
                "result": response.content,
                "processed_images_count": len(image_urls),
                "total_images_count": len(base64_images),
                "is_multiple_images": len(image_urls) > 1
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"调用 LLM 时出错: {e}",
                "result": None,
                "processed_images_count": 0,
                "total_images_count": len(base64_images)
            }

    # ==== 新增结束 ====

    def _normalize_image_input(self, image_sources: Union[str, Dict, List]) -> List[Dict]:
        """
        标准化图片输入，统一处理单张和多张图片的输入格式

        参数:
            image_sources: 可以是以下格式之一：
                1. 字符串（单张本地图片）
                2. 字典（单张图片）：{"path": "路径", "is_local": True/False}
                3. 列表（多张图片）：[字符串, 字典, ...]

        返回:
            list: 标准化后的图片信息列表 [{"path": "路径", "is_local": bool}, ...]
        """
        if isinstance(image_sources, str):
            # 单张本地图片（字符串路径）
            return [{"path": image_sources, "is_local": True}]

        elif isinstance(image_sources, dict):
            # 单张图片（字典格式）
            return [image_sources]

        elif isinstance(image_sources, list):
            # 多张图片（列表格式）
            normalized = []
            for item in image_sources:
                if isinstance(item, str):
                    # 字符串 -> 默认本地图片
                    normalized.append({"path": item, "is_local": True})
                elif isinstance(item, dict):
                    # 字典 -> 直接使用，设置默认值
                    normalized.append({
                        "path": item["path"],
                        "is_local": item.get("is_local", True)
                    })
                else:
                    print(f"警告：跳过无效的图片输入格式: {item}")
            return normalized

        else:
            raise ValueError(f"不支持的图片输入格式: {type(image_sources)}")

    async def analyze_image(self, image_sources: Union[str, Dict, List],
                            prompt: str = "使用中文描述这张图片的内容",
                            is_local: bool = True) -> Dict[str, Any]:
        """
        异步统一的图片分析方法，支持单张或多张图片

        参数:
            image_sources: 图片输入，支持以下格式：
                1. 字符串：单张本地图片路径
                2. 字典：单张图片 {"path": "路径", "is_local": True/False}
                3. 列表：多张图片 [字符串/字典, ...]
            prompt (str): 分析提示词
            is_local (bool): 当image_sources为字符串时的默认类型

        返回:
            dict: 包含分析结果和状态的字典
        """
        try:
            # 处理字符串输入的特殊情况（保持向后兼容）
            if isinstance(image_sources, str):
                image_sources = {"path": image_sources, "is_local": is_local}

            # 标准化输入格式
            normalized_images = self._normalize_image_input(image_sources)

            print(f"准备分析 {len(normalized_images)} 张图片")

            # 并发处理所有图片
            tasks = [
                self._process_single_image(image_info["path"], image_info["is_local"])
                for image_info in normalized_images
            ]

            image_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 过滤成功的图片URL
            image_urls = []
            for i, result in enumerate(image_results):
                if isinstance(result, Exception):
                    print(f"处理第 {i + 1} 张图片时出现异常: {result}")
                elif result is not None:
                    image_urls.append(result)
                else:
                    print(f"跳过第 {i + 1} 张图片（处理失败）")

            if not image_urls:
                return {
                    "success": False,
                    "error": "所有图片处理失败",
                    "result": None,
                    "processed_images_count": 0,
                    "total_images_count": len(normalized_images)
                }

            # 构建消息内容
            content = [
                {
                    "type": "text",
                    "text": prompt,
                }
            ]

            # 添加所有图片
            content.extend([
                {"type": "image_url", "image_url": {"url": url}}
                for url in image_urls
            ])

            # 构建消息
            message = HumanMessage(content=content)

            # 异步调用LLM进行分析
            response = await self.llm.ainvoke([message])

            return {
                "success": True,
                "error": None,
                "result": response.content,
                "processed_images_count": len(image_urls),
                "total_images_count": len(normalized_images),
                "is_multiple_images": len(image_urls) > 1
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"调用LLM时出错: {e}",
                "result": None,
                "processed_images_count": len(image_urls) if 'image_urls' in locals() else 0,
                "total_images_count": len(normalized_images) if 'normalized_images' in locals() else 0
            }

    async def batch_analyze(self, image_batches: List,
                            prompt: str = "使用中文描述这些图片的内容") -> List[Dict]:
        """
        异步批量分析多组图片（每组图片使用相同的prompt）

        参数:
            image_batches (list): 图片批次列表，每个批次可以是单张或多张图片
            prompt (str): 分析提示词

        返回:
            list: 分析结果列表
        """
        # 创建并发任务
        tasks = [
            self.analyze_image(image_batch, prompt)
            for image_batch in image_batches
        ]

        # 并发执行所有批次
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果，添加批次索引
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_result = {
                    "success": False,
                    "error": f"批次 {i + 1} 分析失败: {result}",
                    "result": None,
                    "batch_index": i,
                    "processed_images_count": 0,
                    "total_images_count": 0
                }
            else:
                result["batch_index"] = i
                processed_result = result

            processed_results.append(processed_result)

        return processed_results

    def set_model_config(self, temperature: float = None, timeout: int = None, max_retries: int = None):
        """
        更新模型配置

        参数:
            temperature (float): 新的温度参数
            timeout (int): 新的超时时间
            max_retries (int): 新的最大重试次数
        """
        if temperature is not None:
            self.temperature = temperature
        if timeout is not None:
            self.timeout = timeout
        if max_retries is not None:
            self.max_retries = max_retries

        # 重新初始化模型
        self._initialize_model()
        print("模型配置已更新")


# 异步使用示例
async def main():
    """异步主函数示例"""

    # 初始化图片分析器
    print("=== 初始化图片分析器 ===")
    analyzer = AsyncImageAnalyzer(
        env_path=r'D:\硕士\aiagent\gpt_CWSC\.env',
        model_name="gemini-2.0-flash",
        temperature=0,
        timeout=30
    )

    print("\n" + "=" * 50)

    # # 示例1: 分析单张本地图片（简单字符串）
    # print("\n=== 示例1: 分析单张本地图片（字符串格式） ===")
    # single_image = r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg"
    #
    # result1 = await analyzer.analyze_image(single_image, "描述这张图片的内容")
    # if result1["success"]:
    #     print("分析结果:", result1["result"])
    #     print(f"图片类型: {'多张' if result1['is_multiple_images'] else '单张'}")
    # else:
    #     print("分析失败:", result1["error"])
    #
    # print("\n" + "=" * 50)
    #
    # # 示例2: 分析单张图片（字典格式）
    # print("\n=== 示例2: 分析单张图片（字典格式） ===")
    # single_image_dict = {"path": "https://pic.616pic.com/ys_bnew_img/00/56/23/xn7C2uEu4J.jpg", "is_local": False}
    #
    # result2 = await analyzer.analyze_image(single_image_dict, "详细描述这张网络图片")
    # if result2["success"]:
    #     print("分析结果:", result2["result"])
    #     print(f"图片类型: {'多张' if result2['is_multiple_images'] else '单张'}")
    # else:
    #     print("分析失败:", result2["error"])
    #
    # print("\n" + "=" * 50)
    #
    # # # 示例3: 分析多张图片（列表格式，混合类型）
    # # print("\n=== 示例3: 分析多张图片（列表格式） ===")
    # # multiple_images = [
    # #     r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg",  # 本地图片（字符串）
    # #     {"path": "https://pic.616pic.com/ys_bnew_img/00/56/23/xn7C2uEu4J.jpg", "is_local": False}  # 网络图片（字典）
    # # ]
    # #
    # # result3 = await analyzer.analyze_image(multiple_images, "比较这些图片的内容和风格")
    # # if result3["success"]:
    # #     print("分析结果:", result3["result"])
    # #     print(f"处理图片数: {result3['processed_images_count']}/{result3['total_images_count']}")
    # #     print(f"图片类型: {'多张' if result3['is_multiple_images'] else '单张'}")
    # # else:
    # #     print("分析失败:", result3["error"])
    # #
    # # print("\n" + "=" * 50)
    #
    # # 示例4: 分析多张本地图片（简化列表）
    # print("\n=== 示例4: 分析多张本地图片（简化列表） ===")
    # local_images = [
    #     r"D:\硕士\aiagent\gpt_CWSC\pictures\1.png",
    #     r"D:\硕士\aiagent\gpt_CWSC\pictures\2.png"  # 假设存在
    # ]
    #
    # result4 = await analyzer.analyze_image(local_images, "总结这些图片的共同主题")
    # if result4["success"]:
    #     print("分析结果:", result4["result"])
    #     print(f"处理图片数: {result4['processed_images_count']}/{result4['total_images_count']}")
    # else:
    #     print("分析失败:", result4["error"])
    #
    # print("\n" + "=" * 50)

    # === 示例5: 分析两张 base64 图片 ===
    transparent_png = (

    )
    red_png = (
        """data:image/png;base64,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"""
    )

    result5 = await analyzer.analyze_base64_images([red_png, red_png], "描述这两张 base64 图片")
    if result5["success"]:
        print("分析结果:", result5["result"])
        print(f"处理图片数: {result5['processed_images_count']}/{result5['total_images_count']}")
    else:
        print("分析失败:", result5["error"])

    print("\n" + "=" * 50)

    # # 示例6: 批量分析（每组可以是单张或多张）
    # print("\n=== 示例6: 批量分析 ===")
    # batches = [
    #     r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg",  # 第一组：单张
    #     [  # 第二组：多张
    #         r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg",
    #         {"path": "https://pic.616pic.com/ys_bnew_img/00/56/23/xn7C2uEu4J.jpg", "is_local": False}
    #     ]
    # ]
    #
    # batch_results = await analyzer.batch_analyze(batches, "简要描述图片内容")
    # for result in batch_results:
    #     print(f"\n第 {result['batch_index'] + 1} 组:")
    #     if result["success"]:
    #         print("分析结果:", result["result"])
    #         print(f"图片类型: {'多张' if result['is_multiple_images'] else '单张'}")
    #     else:
    #         print("分析失败:", result["error"])
    #
    # print("\n" + "=" * 50)
    #
    # # 示例7: 并发处理多个独立任务
    # print("\n=== 示例7: 并发处理多个独立任务 ===")
    #
    # # 创建多个独立的分析任务
    # concurrent_tasks = [
    #     analyzer.analyze_image(r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg", "描述图片主要内容"),
    #     analyzer.analyze_image(
    #         {"path": "https://pic.616pic.com/ys_bnew_img/00/56/23/xn7C2uEu4J.jpg", "is_local": False}, "分析图片色彩"),
    #     analyzer.analyze_image([r"D:\硕士\aiagent\gpt_CWSC\pictures\1.png"], "识别图片中的物体")
    # ]
    #
    # # 并发执行所有任务
    # concurrent_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
    #
    # for i, result in enumerate(concurrent_results):
    #     print(f"\n并发任务 {i + 1}:")
    #     if isinstance(result, Exception):
    #         print(f"任务失败: {result}")
    #     elif result["success"]:
    #         print("分析结果:", result["result"][:100] + "..." if len(result["result"]) > 100 else result["result"])
    #     else:
    #         print("分析失败:", result["error"])


# 运行异步示例
if __name__ == "__main__":
    # Python 3.7+
    asyncio.run(main())