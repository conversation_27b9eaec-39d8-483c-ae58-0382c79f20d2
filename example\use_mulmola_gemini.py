import os
import requests
from PIL import Image
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI
import io
import base64

from langchain_openai import ChatOpenAI


class ImageAnalyzer:
    """
    基于Google Gemini的图片分析器类
    支持本地图片和网络图片的智能分析，统一接口处理单张或多张图片
    """

    def __init__(self, env_path=None, model_name="gemini-2.0-flash", temperature=0, timeout=30, max_retries=2):
        """
        初始化图片分析器

        参数:
            env_path (str): .env文件路径，如果为None则从当前目录加载
            model_name (str): 使用的模型名称
            temperature (float): 模型温度参数
            timeout (int): 请求超时时间
            max_retries (int): 最大重试次数
        """
        # 加载环境变量
        if env_path:
            load_dotenv(env_path,override=True)
        else:
            load_dotenv()

        # 获取API密钥
        self.api_key = os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("未找到GOOGLE_API_KEY环境变量，请检查.env文件配置")

        # 初始化模型参数
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.max_retries = max_retries

        # 初始化模型
        self._initialize_model()

    def _initialize_model(self):
        """初始化Google Gemini模型"""
        try:
            self.llm = ChatOpenAI(
                model=self.model_name,
                base_url='https://xiaoai.plus/v1',

                temperature=self.temperature,
                max_tokens=None,
                timeout=self.timeout,
                max_retries=self.max_retries,
                api_key=self.api_key,


            )
            print(f"成功初始化模型: {self.model_name}")
        except Exception as e:
            raise Exception(f"初始化模型失败: {e}")

    def _process_local_image(self, image_path):
        """
        处理本地图片，转换为base64 data URL

        参数:
            image_path (str): 本地图片路径

        返回:
            str: base64编码的data URL
        """
        try:
            # 打开本地图片
            image_pil = Image.open(image_path)
            print(f"成功加载本地图片: {image_path}")

            # 将PIL图片对象转换为base64编码的data URL
            buffered = io.BytesIO()

            # 根据图片格式保存
            image_format = "JPEG"
            if image_path.lower().endswith('.png'):
                image_format = "PNG"
            elif image_path.lower().endswith('.gif'):
                image_format = "GIF"

            image_pil.save(buffered, format=image_format)

            # 编码为base64字符串
            img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")

            # 创建data URL
            mime_type_map = {
                "JPEG": "image/jpeg",
                "PNG": "image/png",
                "GIF": "image/gif"
            }
            mime_type = mime_type_map.get(image_format, "image/jpeg")
            image_data_url = f"data:{mime_type};base64,{img_str}"

            print("本地图片已成功转换为base64 data URL")
            return image_data_url

        except FileNotFoundError:
            print(f"错误: 找不到本地图片文件 {image_path}")
            return None
        except Exception as e:
            print(f"处理本地图片时出错: {e}")
            return None

    def _process_web_image(self, image_url):
        """
        处理网络图片URL

        参数:
            image_url (str): 网络图片URL

        返回:
            str: 图片URL
        """
        print(f"使用网络图片URL: {image_url}")
        return image_url

    def _process_single_image(self, image_source, is_local=True):
        """
        处理单张图片的内部方法

        参数:
            image_source (str): 图片来源，可以是本地路径或网络URL
            is_local (bool): True表示本地图片，False表示网络URL

        返回:
            str: 可用于LLM的图片URL
        """
        if is_local:
            return self._process_local_image(image_source)
        else:
            return self._process_web_image(image_source)

    def _normalize_image_input(self, image_sources):
        """
        标准化图片输入，统一处理单张和多张图片的输入格式

        参数:
            image_sources: 可以是以下格式之一：
                1. 字符串（单张本地图片）
                2. 字典（单张图片）：{"path": "路径", "is_local": True/False}
                3. 列表（多张图片）：[字符串, 字典, ...]

        返回:
            list: 标准化后的图片信息列表 [{"path": "路径", "is_local": bool}, ...]
        """
        if isinstance(image_sources, str):
            # 单张本地图片（字符串路径）
            return [{"path": image_sources, "is_local": True}]

        elif isinstance(image_sources, dict):
            # 单张图片（字典格式）
            return [image_sources]

        elif isinstance(image_sources, list):
            # 多张图片（列表格式）
            normalized = []
            for item in image_sources:
                if isinstance(item, str):
                    # 字符串 -> 默认本地图片
                    normalized.append({"path": item, "is_local": True})
                elif isinstance(item, dict):
                    # 字典 -> 直接使用，设置默认值
                    normalized.append({
                        "path": item["path"],
                        "is_local": item.get("is_local", True)
                    })
                else:
                    print(f"警告：跳过无效的图片输入格式: {item}")
            return normalized

        else:
            raise ValueError(f"不支持的图片输入格式: {type(image_sources)}")

    def analyze_image(self, image_sources, prompt="使用中文描述这张图片的内容", is_local=True):
        """
        统一的图片分析方法，支持单张或多张图片

        参数:
            image_sources: 图片输入，支持以下格式：
                1. 字符串：单张本地图片路径
                2. 字典：单张图片 {"path": "路径", "is_local": True/False}
                3. 列表：多张图片 [字符串/字典, ...]
            prompt (str): 分析提示词
            is_local (bool): 当image_sources为字符串时的默认类型

        返回:
            dict: 包含分析结果和状态的字典
        """
        try:
            # 处理字符串输入的特殊情况（保持向后兼容）
            if isinstance(image_sources, str):
                image_sources = {"path": image_sources, "is_local": is_local}

            # 标准化输入格式
            normalized_images = self._normalize_image_input(image_sources)

            print(f"准备分析 {len(normalized_images)} 张图片")

            # 处理所有图片
            image_urls = []
            for i, image_info in enumerate(normalized_images):
                print(f"处理第 {i + 1}/{len(normalized_images)} 张图片: {image_info['path']}")

                image_url = self._process_single_image(
                    image_info["path"],
                    image_info["is_local"]
                )

                if image_url is not None:
                    image_urls.append(image_url)
                else:
                    print(f"跳过第 {i + 1} 张图片（处理失败）")

            if not image_urls:
                return {
                    "success": False,
                    "error": "所有图片处理失败",
                    "result": None,
                    "processed_images_count": 0,
                    "total_images_count": len(normalized_images)
                }

            # 构建消息内容
            content = [
                {
                    "type": "text",
                    "text": prompt,
                }
            ]

            # 添加所有图片 - 这里是您要求的关键部分
            content.extend([
                {"type": "image_url", "image_url": {"url": url}}
                for url in image_urls
            ])

            # 构建消息
            message = HumanMessage(content=content)

            # 调用LLM进行分析
            response = self.llm.invoke([message])
            return {
                "success": True,
                "error": None,
                "result": response.content,
                "processed_images_count": len(image_urls),
                "total_images_count": len(normalized_images),
                "is_multiple_images": len(image_urls) > 1
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"调用LLM时出错: {e}",
                "result": None,
                "processed_images_count": len(image_urls) if 'image_urls' in locals() else 0,
                "total_images_count": len(normalized_images) if 'normalized_images' in locals() else 0
            }

    def batch_analyze(self, image_batches, prompt="使用中文描述这些图片的内容"):
        """
        批量分析多组图片（每组图片使用相同的prompt）

        参数:
            image_batches (list): 图片批次列表，每个批次可以是单张或多张图片
            prompt (str): 分析提示词

        返回:
            list: 分析结果列表
        """
        results = []
        for i, image_batch in enumerate(image_batches):
            print(f"\n正在分析第 {i + 1}/{len(image_batches)} 组图片...")
            result = self.analyze_image(image_batch, prompt)
            result["batch_index"] = i
            results.append(result)

        return results

    def set_model_config(self, temperature=None, timeout=None, max_retries=None):
        """
        更新模型配置

        参数:
            temperature (float): 新的温度参数
            timeout (int): 新的超时时间
            max_retries (int): 新的最大重试次数
        """
        if temperature is not None:
            self.temperature = temperature
        if timeout is not None:
            self.timeout = timeout
        if max_retries is not None:
            self.max_retries = max_retries

        # 重新初始化模型
        self._initialize_model()
        print("模型配置已更新")


# 使用示例
if __name__ == "__main__":

    # 初始化图片分析器
    print("=== 初始化图片分析器 ===")
    analyzer = ImageAnalyzer(
        env_path=r'D:\硕士\aiagent\gpt_CWSC\.env',
        model_name="gemini-2.0-flash",
        temperature=0,
        timeout=30
    )

    print("\n" + "=" * 50)

    # 示例1: 分析单张本地图片（简单字符串）
    print("\n=== 示例1: 分析单张本地图片（字符串格式） ===")
    single_image = r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg"

    result1 = analyzer.analyze_image(single_image, "描述这张图片的内容")
    if result1["success"]:
        print("分析结果:", result1["result"])
        print(f"图片类型: {'多张' if result1['is_multiple_images'] else '单张'}")
    else:
        print("分析失败:", result1["error"])

    print("\n" + "=" * 50)

    # # 示例2: 分析单张图片（字典格式）
    # print("\n=== 示例2: 分析单张图片（字典格式） ===")
    # single_image_dict = {"path": "https://pic.616pic.com/ys_bnew_img/00/56/23/xn7C2uEu4J.jpg", "is_local": False}
    #
    # result2 = analyzer.analyze_image(single_image_dict, "详细描述这张网络图片")
    # if result2["success"]:
    #     print("分析结果:", result2["result"])
    #     print(f"图片类型: {'多张' if result2['is_multiple_images'] else '单张'}")
    # else:
    #     print("分析失败:", result2["error"])
    #
    # print("\n" + "=" * 50)
    #
    # # 示例3: 分析多张图片（列表格式，混合类型）
    # print("\n=== 示例3: 分析多张图片（列表格式） ===")
    # multiple_images = [
    #     r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg",  # 本地图片（字符串）
    #     {"path": "https://pic.616pic.com/ys_bnew_img/00/56/23/xn7C2uEu4J.jpg", "is_local": False}  # 网络图片（字典）
    # ]
    #
    # result3 = analyzer.analyze_image(multiple_images, "比较这些图片的内容和风格")
    # if result3["success"]:
    #     print("分析结果:", result3["result"])
    #     print(f"处理图片数: {result3['processed_images_count']}/{result3['total_images_count']}")
    #     print(f"图片类型: {'多张' if result3['is_multiple_images'] else '单张'}")
    # else:
    #     print("分析失败:", result3["error"])
    #
    # print("\n" + "=" * 50)
    #
    # # 示例4: 分析多张本地图片（简化列表）
    # print("\n=== 示例4: 分析多张本地图片（简化列表） ===")
    # local_images = [
    #     r"D:\硕士\aiagent\gpt_CWSC\pictures\1.png",
    #     r"D:\硕士\aiagent\gpt_CWSC\pictures\2.png"  # 假设存在
    # ]
    #
    # result4 = analyzer.analyze_image(local_images, "总结这些图片的共同主题")
    # if result4["success"]:
    #     print("分析结果:", result4["result"])
    #     print(f"处理图片数: {result4['processed_images_count']}/{result4['total_images_count']}")
    # else:
    #     print("分析失败:", result4["error"])
    #
    # print("\n" + "=" * 50)
    #
    # # 示例5: 批量分析（每组可以是单张或多张）
    # print("\n=== 示例5: 批量分析 ===")
    # batches = [
    #     r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg",  # 第一组：单张
    #     [  # 第二组：多张
    #         r"D:\硕士\aiagent\gpt_CWSC\example\content\111.jpg",
    #         {"path": "https://pic.616pic.com/ys_bnew_img/00/56/23/xn7C2uEu4J.jpg", "is_local": False}
    #     ]
    # ]
    #
    # batch_results = analyzer.batch_analyze(batches, "简要描述图片内容")
    # for result in batch_results:
    #     print(f"\n第 {result['batch_index'] + 1} 组:")
    #     if result["success"]:
    #         print("分析结果:", result["result"])
    #         print(f"图片类型: {'多张' if result['is_multiple_images'] else '单张'}")
    #     else:
    #         print("分析失败:", result["error"])